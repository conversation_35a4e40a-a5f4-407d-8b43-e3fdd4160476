</br>
<div class="alert alert-success">
    <strong>Dominios Contratados</strong>
     @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevodominio"></button>
    @endif
</div>
@include ('CLIENTES.DOMINIO.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped table-sm">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Nombre del Dominio</th>
        <th width="30px">Tipo de Dominio</th>
    </tr>
    </thead>
    <tbody>
    @foreach($dominios as $dominio)
        <tr>
            <td>{{$dominio->establecimiento}}</td>
            <td>{{$dominio->nombre_dominio}}</td>
            <td>{{$dominio->tipo_dominio}}</td>
            <td>
                <form action="{{Route('dominios.destroy', $dominio->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.DOMINIO.modal_delete')
                </form>
                @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#ddominio{{$dominio->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#edominio{{$dominio->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.DOMINIO.modal_edit')
    @endforeach

    </tbody>
</table>
