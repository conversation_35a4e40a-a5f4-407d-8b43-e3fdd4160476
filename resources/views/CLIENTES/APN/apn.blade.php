</br>
<div class="alert alert-success">
    <strong>Enlaces APN Contratados</strong>
 @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevaapn"></button>
    @endif
</div>
@include ('CLIENTES.APN.modal')
@include ('CLIENTES.FRAGMENTO.info')
@include ('CLIENTES.FRAGMENTO.error')
{{--@include ('CLIENTES.APN.modal_delete')--}}
<table class="table table-hover table-striped table-sm">
    <thead>
    <tr>
        <th width="10px">Establecimento</th>
        <th width="10px">Movil</th>
        <th width="30px">Nombre</th>
        <th width="20px">Cargo</th>
        <th width="20px">IP</th>
        <th width="20px">Acceso</th>
        <th width="20px">Estado</th>
    </tr>
    </thead>
    <tbody>

    @foreach($apns as $apn)
        <tr>
            <td>{{$apn->establecimiento}}</td>
            <td style="color:black">{{$apn->no_movil}}</td>
            <td>{{$apn->nombre}}</td>
            <td>{{$apn->cargo}}</td>
            <td>{{$apn->ip}}</td>
             <td>{{$apn->acceso}}</td>
            <td> @if ($apn->estado=='1')
                Habilitado
            @else
                Desabilitado
            @endif

            </td>
            <td>

                <form action="{{Route('apns.destroy', $apn->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.APN.modal_delete')
                </form>
		 @if(auth()->user()->admin_clientes=='1')
		<button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#d{{$apn->id}}"></button>
                    <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#e{{$apn->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.APN.modal_edit')
    @endforeach

    </tbody>
</table>

