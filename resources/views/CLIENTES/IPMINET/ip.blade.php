</br>
<div class="alert alert-success">
    <strong>Enlaces Dedicados Contratados</strong>
    @if(auth()->user()->UEB<>'invitado')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevadedicada"></button>
    @endif
</div>
@include ('CLIENTES.DEDICADAS.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped table-sm" id="mytable">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">INA</th>
        <th width="30px">Identificador</th>
        <th width="30px">Lan</th>
        <th width="20px">Wan</th>
        <th width="20px">Velocidad</th>
        <th width="20px">Código</th>
    </tr>
    </thead>
    <tbody>
    @foreach($dedicadas as $dedicada)
        <tr>
            <td>{{$dedicada->establecimiento}}</td>
            <td>{{$dedicada->ina}}</td>
            <td>{{$dedicada->identificador}}</td>
            <td>{{$dedicada->lan}}</td>
            <td>{{$dedicada->wan}}</td>
            <td>{{$dedicada->velocidad}}</td>
            <td>{{$dedicada->codigo->descripcion}}</td>
            <td>
                <form action="{{Route('dedicadas.destroy', $dedicada->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.DEDICADAS.modal_delete')
                </form>
                @if(auth()->user()->UEB<>'invitado')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#ip{{$dedicada->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#ededicada{{$dedicada->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.DEDICADAS.modal_edit')
    @endforeach

    </tbody>

</table>

