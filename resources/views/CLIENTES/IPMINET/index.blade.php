@extends('CLIENTES.plantilla')
@section('contenido')


    <nav class="navbar navbar-expand-sm border border-success navbar-dark ">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        {{--<input class="form-control" id="myInput" type="text" placeholder="Buscar..">--}}
                    {{Form::open(['Route'=> 'ipminets.index', 'method'=>'GET', 'class'=>'form-inline pull-right'])}}
                    <div class="form-group">
                        {{Form::text('categoria', null, ['class'=>'form-control','placeholder'=>'categoria'])}}
                    </div>
                    <div class="form-group">
                        {{Form::text('lan', null, ['class'=>'form-control','placeholder'=>'Lan'])}}
                    </div>
                    <div class="form-group">
                        {{Form::text('wan', null, ['class'=>'form-control','placeholder'=>'Wan'])}}
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-default">
                            Buscar
                        </button>
                    </div>
                    {{Form::close()}}
    </nav>
    &nbsp;

    <div class="alert alert-success">
        <strong>Asignar Nueva IPS  </strong>
        @if(auth()->user()->UEB<>'invitado')
            <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevaip"></button>
        @endif
    </div>
 <div class="alert alert-info">
       Cantida de Host por mascaras 
<span class="badge badge-danger">/32->1pc </span>  
<span class="badge badge-warning">/31->2pc </span> 
<span class="badge badge-danger">/30->4pc </span>  
<span class="badge badge-warning">/29->8pc </span>
<span class="badge badge-danger">/28->16pc </span>  
<span class="badge badge-warning">/27->32pc </span>
<span class="badge badge-danger">/26->64pc </span>  
<span class="badge badge-warning">/25->128pc </span>
<span class="badge badge-danger">/24->254pc </span>  

 </div>
    @include ('CLIENTES.IPMINET.modal')
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    {!! $trans->render() !!}
    {{--<div class="alert alert-success">--}}
        {{--<strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> IPMINET</strong>--}}
    {{--</div>--}}

    <table class="table table-hover table-striped table-sm" id="mytabla">
        <thead>
        <tr>
            <th width="600px">Empresa</th>
            <th width="10px">Entidad</th>
            <th width="30px">Categoria</th>
            <th width="40px">Lan</th>            
            <th width="40px">wan</th>
            <th width="40px">Observaciones</th>
        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>

                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->entidad}}</td>
                <td>{{$tran->categoria}}</td>
                <td>{{$tran->lan}}</td>             
                <td>{{$tran->wan}}</td>
                <td>{{$tran->observacione}}</td>
                    <td>
                        <form action="{{Route('ipminets.destroy', $tran->id)}}" method="post">
                            {{csrf_field()}}
                            <input type="hidden" name="_method" value="delete">
                            @include ('CLIENTES.IPMINET.modal_delete')
                        </form>
                    {{--<td >--}}
                        {{--<a href="{{Route('clientes.show', $tran->id, $tran->empresa->nombre)}}" class="btn btn-success fas fa-eye float-right "></a>--}}
                    {{--</td>--}}
                    @if(auth()->user()->UEB<>'invitado')
                        <td width="5px">  <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#eip{{$tran->id}}"></button>
                        <td width="5px"> <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dip{{$tran->id}}"></button> </td>
                        @endif
                        </td>
                        <td>
                        </td>
                        <td>
                        </td>
            </tr> @include ('CLIENTES.IPMINET.modal_edit')
        @endforeach

        </tbody>
    </table>
    {!! $trans->render() !!}
    <script type="text/javascript">
        $(document).ready(function(){
            $('#model').combobox();
        });


    </script>
<script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>

@endsection
