@extends('CLIENTES.plantillamonitor')
@section('contenido')
   <?php

$title = "";

$servers = array(
    
    'ALIMATIC HABANA' => array(
        'ip' => '*************',
        'info' => '************',
        'ed' => 'ED8029',
        'purpose' => 'ICMP'
    ),

    'GEIA' => array(
        'ip' => '**************',
        'info' => '**************',
        'ed' => 'FD0213',
        'purpose' => 'ICMP'
    ),
  

);

if (isset($_GET['host'])) {
    $host = $_GET['host'];


    if (isset($servers[$host])) {
        header('Content-Type: application/json');

        $return = array(
            'status' => test($servers[$host])
        );

        echo json_encode($return);
        exit;
    } else {
        header("HTTP/1.1 404 Not Found");
    }
}

$names = array();
foreach ($servers as $name => $info) {
    $names[$name] = md5($name);
}


?> 
 
    <div class="alert alert-success">
      IP MONITOR
    </div>

    <div class="container">
            <center><h1><?php echo $title; ?></h1></center>
            <table class="table">
                <thead>
                    <tr>
                        <th></th>
                        <th>SERVIDOR</th>
                        <th>DIRECCION IP</th>
                        <th>INDENTIFICADOR</th>
                        <th>SERVICIO</th>
                    </tr>
                </thead>
                <tbody>

                    <?php foreach ($servers as $name => $server): ?>

                        <tr id="<?php echo md5($name); ?>">
                            <td><i class="icon-spinner icon-spin icon-large"></i></td>
                            <td class="name"><?php echo $name; ?></td>
                            <td><?php echo $server['info']; ?></td>
                            <td><?php echo $server['ed']; ?></td>
                            <td><?php echo $server['purpose']; ?></td>

                        </tr>

                    <?php endforeach; ?>

                </tbody>
            </table>
        </div>

 <script type="text/javascript">

            function test(host, hash) {
                // Fork it
                var request;

                // fire off the request to /form.php
                request = $.ajax({ 
                   // url: "http://conexiones.alinet.cu/ver_monitor",
                    route: "clientes.monitor",
                    type: "get",
                    data: {
                        host: host
                    },
                    beforeSend: function () {
                        $('#' + hash).children().children().css({'visibility': 'visible'});
                    }
                });

                // callback handler that will be called on success
                request.done(function (response, textStatus, jqXHR) {
                    var status = response.status;
                    var statusClass;
                    if (status) {
                        statusClass = 'success';
                    } else {
                        statusClass = 'error';
                    }

                    $('#' + hash).removeClass('success error').addClass(statusClass);
                });

                // callback handler that will be called on failure
                request.fail(function (jqXHR, textStatus, errorThrown) {
                    // log the error to the console
                    console.error(
                        "The following error occured: " +
                            textStatus, errorThrown
                    );
                });


                request.always(function () {
                    $('#' + hash).children().children().css({'visibility': 'hidden'});
                })

            }

            $(document).ready(function () {

                var servers = <?php echo json_encode($names); ?>;
                var server, hash;

                for (var key in servers) {
                    server = key;
                    hash = servers[key];

                    test(server, hash);
                    (function loop(server, hash) {
                        setTimeout(function () {
                            test(server, hash);

                            loop(server, hash);
                        }, 30000);
                    })(server, hash);
                }

            });
        </script>
   
<?php

  class CheckDevice {

        public function myOS(){
            if (strtoupper(substr(PHP_OS, 0, 3)) === (chr(87).chr(73).chr(78)))
                return true;

            return false;
        }

        public function ping($ip_addr){
            if ($this->myOS()){
                if (!exec("ping -n 1 -w 1 ".$ip_addr." 2>NUL > NUL && (echo 0) || (echo 1)"))
                    return true;


            } else {
                if (!exec("ping -q -c1 ".$ip_addr." >/dev/null 2>&1 ; echo $?"))
                    return true;
            }

            return false;
        }
    }

    function test($server) {
     if ((new CheckDevice())->ping($server['ip']))
         return true;
    else 
        return false;
 }

function in_array_r($needle, $haystack, $strict = false) {
    foreach ($haystack as $item) {
        if (($strict ? $item === $needle : $item == $needle) || (is_array($item) && in_array_r($needle, $item, $strict))) {
            return true;
        }
    }

    return false;
}

?>
  
@endsection
