</br>
<div class="alert alert-success">
    <strong><PERSON><PERSON><PERSON> de ALINET Contratados</strong>
    @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevopaquete"></button>
    @endif
</div>
@include ('CLIENTES.PAQUETE.modal')
@include ('CLIENTES.FRAGMENTO.info')
<table class="table table-hover table-striped">
    <thead>
    <tr>
        <th width="10px">HOSTING</th>
        <th width="30px">CORREO</th>
        <th width="30px">NUBE</th>
        <th width="20px">APN</th>
        <th width="20px">PRECIO</th>
    </tr>
    </thead>
    <tbody>
    @foreach($paquetes as $paquete)
        <tr>
            <td>{{$paquete->hosting}}</td>
            <td>{{$paquete->correo}}</td>
            <td>{{$paquete->nube}}</td>
            <td>{{$paquete->apn}}</td>
            <td>{{$paquete->codigo->precio}}</td>
            <td>
                <form action="{{Route('paquetes.destroy', $paquete->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.PAQUETE.modal_delete')
                </form>
                @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dpaquete{{$paquete->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#epaquete{{$paquete->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.PAQUETE.modal_edit')
    @endforeach

    </tbody>
</table>
