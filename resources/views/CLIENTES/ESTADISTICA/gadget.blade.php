<style type="text/css">
    #opaco { opacity:0.5; }
    #borde {
        border-left-width: 5px;
    }
</style>
<div class="col-md-12">
    <br>
</div>
<script src="../../amcharts/amcharts.js" type="text/javascript"></script>
<script src="../amcharts/serial.js" type="text/javascript"></script>

<?php
$tapn=0;
$tdedicada=0;
$tcorreo=0;
$tnube=0;
$tc=0;
$th=0;
$tn=0;
$ta=0;
$total=0;
$papn = 0; $pd = 0; $pc = 0; $pa = 0; $pn = 0; $ph = 0;
$tsicema = 0;
?>
@foreach($apns as $apn)
    @php
     $tapn = $tapn + $apn->codigo->precio
    @endphp
@endforeach

@foreach($dedicadas as $dedicada)
    @php
        $tdedicada = $tdedicada + $dedicada->codigo->precio
    @endphp
@endforeach
@foreach($nubes as $nube)
    @php
        $tnube = $tnube + $nube->codigo->precio
    @endphp
@endforeach
@foreach($correos as $correo)
    @php
        $tcorreo = $tcorreo + $correo->codigo->precio
    @endphp
@endforeach

@foreach($hostings as $hosting)
    @php
        $th = $th + $hosting->codigo->precio
    @endphp
@endforeach

@foreach($nubes as $nube)
    @php
        $tn = $tn + $nube->codigo->precio
    @endphp
@endforeach
@foreach($correos as $correo)
    @php
        $tc = $tc + $correo->codigo->precio
    @endphp
@endforeach

@foreach($audioconferencias as $au)
    @php
        $ta = $ta + ($au->codigo->precio * $au->cantidad_punto)
    @endphp
@endforeach
@foreach($sicemas as $sicema)
    @php
        $tsicema = $tsicema + ($sicema->precio)
    @endphp
@endforeach
@php
    $total= $tapn + $tdedicada + $tc + $th + $tn + $ta ;
    $totall= $tapn + $tdedicada + $tc + $th + $tn + $ta ;
if ($tapn <> 0){
$papn = round(($tapn*100)/$total,2);
}
if($tdedicada <> 0){
$pd = round(($tdedicada*100)/$total,2);
}
if ($tc <> 0){
$pc = round(($tc*100)/$total,2);
}
if ($th <> 0){
$ph = round(($th*100)/$total,2);
}
if($tn <> 0){
$pn = round(($tn*100)/$total,2);
}
if ($ta <> 0){
$pa = round(($ta*100)/$total,2);
}
//if ($sicema <> 0){
//$pmodul = round(($modul*100)/$total,2);
//}
@endphp
<script>
     var chart;

    var chartData = [
        {
            "country": "APN",
            "visits": '{{$tapn}}',
            "color": "#FF0F00"
        },
         {
            "country": "Dedicada",
            "visits": '{{$tdedicada}}',
            "color": "#FF6600"
        },
        {
            "country": "CORREO",
            "visits": '{{$tcorreo}}',
            "color": "#FF0F00"
        },
        {
            "country": "NUBE",
            "visits": '{{$tnube}}',
            "color": "#FF0F00"
        },
      
        {
            "country": "Hosting",
            "visits": '{{$th}}',
            "color": "#FF9E01"
        },
        {
            "country": "Sicema SQL",
            "visits": '{{$tsicema}}',
            "color": "#FCD202"
        }
    ];

    AmCharts.ready(function () {
        // SERIAL CHART
        chart = new AmCharts.AmSerialChart();
        chart.dataProvider = chartData;
        chart.categoryField = "country";
        // the following two lines makes chart 3D
        chart.depth3D = 20;
        chart.angle = 30;

        // AXES
        // category
        var categoryAxis = chart.categoryAxis;
        categoryAxis.labelRotation = 90;
        categoryAxis.dashLength = 5;
        categoryAxis.gridPosition = "start";

        // value
        var valueAxis = new AmCharts.ValueAxis();
        valueAxis.title = "Servicios";
        valueAxis.dashLength = 5;
        chart.addValueAxis(valueAxis);

        // GRAPH
        var graph = new AmCharts.AmGraph();
        graph.valueField = "visits";
        graph.colorField = "color";
        graph.balloonText = "<span style='font-size:14px'>[[category]]: <b>[[value]]</b></span>";
        graph.type = "column";
        graph.lineAlpha = 0;
        graph.fillAlphas = 1;
        chart.addGraph(graph);

        // CURSOR
        var chartCursor = new AmCharts.ChartCursor();
        chartCursor.cursorAlpha = 0;
        chartCursor.zoomable = false;
        chartCursor.categoryBalloonEnabled = false;
        chart.addChartCursor(chartCursor);

        chart.creditsPosition = "top-right";


        // WRITE
        chart.write("chartdiv2");
    });

</script>
</head>

<body>
<div class="row">

    <div class="col-sm-8" id="chartdiv2" style="width:600px; height:200px;"></div>
</div>


</body>


