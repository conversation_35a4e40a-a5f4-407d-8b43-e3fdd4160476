</br>
<div class="alert alert-success ">
    <strong>Solicitudes</strong>
     @if(auth()->user()->admin_clientes=='1')
        <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevasolicitud"></button>
    @endif
</div>
@include ('CLIENTES.SOLICITUDES.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
@foreach($solicitudes as $solicitud)
    <div class="card">
        <div class="card-header alert-warning">   No Orden: ({{$solicitud->id}})  {{$solicitud->titulo}} <spam class="badge badge-info ">Solicitado el:{{$solicitud->created_at}}</spam>
             @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-link  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dsolicitud{{$solicitud->id}}"></button>
                <button class="btn btn-link fas fa-edit float-right " data-toggle="modal" data-target="#esolicitud{{$solicitud->id}}"></button>
            @endif

            @if(auth()->user()->UEB<>'invitado')

                <button class="btn  btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevaconciliacion{{$solicitud->id}}"></button>
            @endif
 		 <a href="{{Route('solicitudes.show',$solicitud->id)}}" class="btn btn-link fas fa-eye float-right"></a>
        </div>
        <div class="card-body">{{$solicitud->dato}}
        </div>
        <div class="card-footer">{{$solicitud->observacion}}</div>
    </div>

    </br>
    @include ('CLIENTES.SOLICITUDES.modal_edit')
    @include ('CLIENTES.SOLICITUDES.modal_add')
    <form action="{{Route('solicitudes.destroy', $solicitud->id)}}" method="post">
        {{csrf_field()}}
        <input type="hidden" name="_method" value="delete">
        @include ('CLIENTES.SOLICITUDES.modal_delete')
    </form>
@endforeach
