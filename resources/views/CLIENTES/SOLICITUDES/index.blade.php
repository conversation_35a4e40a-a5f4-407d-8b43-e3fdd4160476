@extends('CLIENTES.plantilla')
@section('contenido')
</br>
<nav class="navbar navbar-expand-sm border border-success navbar-dark">
    <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
    <h3 class="text-xs font-weight-bold fas fa-home text-warning mb-1">Listado de  solicitudes</h3>
    {{--<input class="form-control" id="myInput" type="text" placeholder="Buscar..">--}}
</nav>
</br>
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
@foreach($trans as $tran)

    <div class="card">
        <div class="card-header alert-warning" id="myTable">
            No Orden: ({{$tran->id}})  
            @foreach($empre as $empresa)
                @if ($empresa->id === $tran->cliente->empresa_id)
              
                {{$empresa->nombre}}
                    
                @endif
            @endforeach
               <spam class="badge badge-danger">{{$tran->titulo}}</spam>
               UEB ALIMATIC {{$tran->uebalimatic}}
		<a href="{{Route('solicitudes.show',$tran->id)}}" class="btn btn-danger  fas fa-eye float-right"></a>
        </div>
        <div class="card-body">{{$tran->dato}}
        </div>
        <div class="card-footer">{{$tran->observacion}}</div>
    </div>
    </br>
@endforeach

@endsection
