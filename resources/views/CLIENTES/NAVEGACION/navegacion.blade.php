</br>
<div class="alert alert-success">
    <strong>Navegacion Contratada</strong>
    @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevonavegacion"></button>
    @endif
</div>
@include ('CLIENTES.NAVEGACION.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-sm table-striped">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Acceso</th>
        <th width="30px">Ip de Navegación</th>
    </tr>
    </thead>
    <tbody>
    @foreach($navegacions as $navegacion)
        <tr>
            <td>{{$navegacion->establecimiento}}</td>
            <td>{{$navegacion->acceso}}</td>
            <td>{{$navegacion->ip_navegacion}}</td>
            <td>
                <form action="{{Route('navegacions.destroy', $navegacion->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.NAVEGACION.modal_delete')
                </form>
                @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dnavegacion{{$navegacion->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#enavegacion{{$navegacion->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.NAVEGACION.modal_edit')
    @endforeach

    </tbody>
</table>
