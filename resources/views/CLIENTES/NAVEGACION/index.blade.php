@extends('CLIENTES.plantilla')
@section('contenido')
    <?php
    $acce=array();
    $ip=array();
    ?>
    @foreach ($trans as $tran)
    <?php
    $acce[$tran->acceso]= $tran->acceso;
    $ip[$tran->ip_navegacion]=$tran->ip_navegacion;
    ?>
    @endforeach
   <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
   </nav>
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> NAVEGACION  @php echo count($trans) @endphp </strong>
    </div>
    <table class="table table-hover table-sm table-striped">
        <thead>
        <tr>

            <th width="30px">Empresa</th>
            <th width="30px">Acceso</th>
            <th width="30px">IP-Navegacion</th>
        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>

                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->acceso}}</td>
                <td>{{$tran->ip_navegacion}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    {!! $trans->render() !!}
    {{--Funcion de buscar en el combo box-se le agrega al control arriba el id de tipo modelo--}}
    <script type="text/javascript">
        $(document).ready(function(){
            $('.modelo').combobox();
        });
    </script>
<script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
@endsection