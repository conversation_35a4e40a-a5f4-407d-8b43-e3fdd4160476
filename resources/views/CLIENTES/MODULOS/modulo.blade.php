</br>
<div class="alert alert-success">
    <strong><PERSON><PERSON><PERSON></strong>
    @if(auth()->user()->UEB<>'invitado')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevomodulo"></button>
    @endif
</div>
@include ('CLIENTES.MODULOS.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped">
    <thead>
    <tr>
        <th width="20px">identificador</th>
        <th width="20px">Establecimiento</th>
        <th width="10px">Version del Ejecutable</th>
        <th width="10px">Version de la Base de datos</th>
        <th width="20px">codigo</th>
        <th width="20px">precio</th>
    </tr>
    </thead>
    <tbody>
    @foreach($modulos as $modulo)
        <tr>
            <td>{{$modulo->identificador}}</td>
            <td>{{$modulo->establecimiento}}</td>
            <td width="200px">{{$modulo->version_ejecutable}}</td>
            <td  width="300px">{{$modulo->version_bd}}</td>
            <td>{{$modulo->codigo}}</td>
            <td>{{$modulo->precio}}</td>
            <td>
                <form action="{{Route('modulos.destroy', $modulo->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.MODULOS.modal_delete')
                </form>
                @if(auth()->user()->UEB<>'invitado')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dmodulo{{$modulo->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#emodulo{{$modulo->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.MODULOS.modal_edit')
    @endforeach

    </tbody>
</table>