</br>
<div class="alert alert-success">
    <strong>Nubes Contratada</strong>
     @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevonanube"></button>
    @endif
</div>
@include ('CLIENTES.NUBE.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Usuario</th>
        <th width="10px">Espacio de la Nube</th>
    </tr>
    </thead>
    <tbody>
    @foreach($nubes as $nube)
        <tr>
            <td>{{$nube->establecimiento}}</td>
            <td>{{$nube->usuario}}</td>
            <td>{{$nube->espacio}}</td>
            <td>
                <form action="{{Route('nubes.destroy', $nube->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.NUBE.modal_delete')
                </form>
                @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dnube{{$nube->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#enube{{$nube->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.NUBE.modal_edit')
    @endforeach

    </tbody>
</table>
