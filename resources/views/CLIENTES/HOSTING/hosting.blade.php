</br>
<div class="alert alert-success">
    <strong>Hosting Contratados</strong>
    @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevohosting"></button>
    @endif
</div>
@include ('CLIENTES.HOSTING.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped table-sm">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Nombre del Sitio</th>
        <th width="30px">Posee BD</th>
        <th width="30px">Espacio del Sitio</th>
        <th width="30px">Plataforma del Sitio</th>
    </tr>
    </thead>
    <tbody>
    @foreach($hostings as $hosting)
        <tr>
            <td>{{$hosting->establecimiento}}</td>
            <td>{{$hosting->nombre_sitio}}</td>
            <td>{{$hosting->base_de_datos}}</td>
            <td>{{$hosting->espacio}}</td>
            <td>{{$hosting->plataforma}}</td>
            <td>
                <form action="{{Route('hostings.destroy', $hosting->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.HOSTING.modal_delete')
                </form>
                 @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dhosting{{$hosting->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#ehosting{{$hosting->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.HOSTING.modal_edit')
    @endforeach

    </tbody>
</table>
