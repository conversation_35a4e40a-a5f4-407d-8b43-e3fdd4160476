@extends('CLIENTES.plantilla')
@section('contenido')
   <?php
      $usu=array();
      $anc=array();
   ?>
    @foreach ($trans as $tran)
    <?php
    $usu[$tran->usuario]= $tran->usuario;
    $anc[$tran->anclaje]=$tran->anclaje;
    ?>
    @endforeach
    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        {!! Form::open(['route'=>'conmutadas.index', 'method'=>'get', 'class'=>'form-inline','role'=>'search']) !!}
        {!! form::select('usuario',$usu,null,['class'=>'form-control mr-sm-2 modelo','placeholder'=>'Usuario' ] ) !!}
        {!! form::select('anclaje',$anc ,null,['class'=>'form-control mr-sm-2 modelo','placeholder'=>'Anclaje' ] ) !!}
        <button class="btn btn-success" type="submit">Buscar</button>
        &nbsp;
        {!! Form::close() !!}
    </nav>
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> CONMUTADAS</strong>
    </div>
    <table class="table table-hover table-striped">
        <thead>
        <tr>
            <th width="10px">ID</th>
            <th width="30px">Empresa</th>
            <th width="30px">Usuario</th>
            <th width="20px">Anclaje</th>
            <th width="20px">Acceso</th>
            <th width="20px">Usuario de Navegación</th>
            <th width="20px">Codigo</th>
        </tr>
        </thead>
        <tbody>
        @foreach($trans as $tran)
            <tr>
                <td>{{$tran->id}}</td>
                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->usuario}}</td>
                <td>{{$tran->anclaje}}</td>
                <td>{{$tran->acceso}}</td>
                <td>{{$tran->usuario_navegacion}}</td>
                <td>{{$tran->codigo->codigo}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
   {!! $trans->render() !!}
    {{--Funcion de buscar en el combo box-se le agrega al control arriba el id de tipo modelo--}}
    <script type="text/javascript">
        $(document).ready(function(){
            $('.modelo').combobox();
        });
    </script>
@endsection