</br>
{{--<div class="alert alert-success">--}}
    {{--<strong>Centros Contables</strong>--}}
{{--</div>--}}

{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped">
    <thead>
    <tr>
        <th width="20px">Empresa</th>
        <th width="10px">Entidad</th>
        <th width="10px">Estado</th>
    </tr>
    </thead>
    <tbody>
    @php
        $no = 0;
        $si = 0;
        $no_procede = 0;
    @endphp
    @foreach($trans as $tran)
            @if ($tran->activo_fijo === "no")
               @php
                $no ++
               @endphp
            @endif
    @endforeach
    @foreach($trans as $tran)
        @if ($tran->activo_fijo === "si")
            @php
                $si ++
            @endphp
        @endif
    @endforeach
    @foreach($trans as $tran)
        @if ($tran->activo_fijo === "no procede")
            @php
                $no_procede ++
            @endphp
        @endif
    @endforeach
        <div class="col-1"> <span class="badge badge-danger">{{$no}} Pendientes</span></div>
        <div class="col-1"> <span class="badge badge-success">{{$si}} Instalados</span></div>
        <div class="col-1"> <span class="badge badge-info">{{$no_procede}} No Procede</span></div>
    @foreach($trans as $tran)
        <tr>
        <tr>
            @if ($tran->activo_fijo === "no")
            @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                    <td>{{$empresa->nombre}}</td>
                    <td>{{$tran->establecimiento}}</td>
                    <td>No instalado</td>
                @endif
            @endforeach
              @endif
    @endforeach
    </tbody>
</table>