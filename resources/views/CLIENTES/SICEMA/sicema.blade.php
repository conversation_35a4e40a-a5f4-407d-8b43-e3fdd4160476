</br>
<div class="alert alert-success">
    <strong>Centros Contables</strong>
    @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevosicema"></button>
    @endif
</div>
@include ('CLIENTES.SICEMA.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped">
    <thead>
    <tr>
        <th width="20px">Entidad</th>
        <th width="10px">Version del Ejecutable</th>
        <th width="10px">Version de la Base de datos</th>
        <th width="10px">Precio</th>

    </tr>
    </thead>
    <tbody>
    @foreach($sicemas as $sicema)
        <tr>
            <td>{{$sicema->establecimiento}}</td>
            <td width="200px">{{$sicema->version_ejecutable}}</td>
            <td  width="300px">{{$sicema->version_bd}}</td>
             <td  width="300px">{{$sicema->codigo->precio}}</td>

            <td>
                <form action="{{Route('sicemas.destroy', $sicema->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.SICEMA.modal_delete')
                </form>

                @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dsicema{{$sicema->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#esicema{{$sicema->id}}"></button>
                @endif
                <button class="btn btn-info  fas fa-eye float-right " data-toggle="modal" data-target="#vsicema{{$sicema->id}}"></button>
            </td>
        </tr>
        @include ('CLIENTES.SICEMA.modal_edit')
        @include ('CLIENTES.SICEMA.modal_ver')
    @endforeach

    </tbody>
</table>
