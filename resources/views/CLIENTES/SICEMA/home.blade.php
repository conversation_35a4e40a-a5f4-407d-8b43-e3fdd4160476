</br>
{{--<div class="alert alert-success">--}}
    {{--<strong>Centros Contables</strong>--}}
{{--</div>--}}

{{--@include ('CLIENTES.FRAGMENTO.info')--}}

    <script src="../amcharts/amcharts.js" type="text/javascript"></script>
    <script src="../amcharts/serial.js" type="text/javascript"></script>

    <script>
        var chart;

        var chartData = [
            {
                "year": "Contabilidad",
                "noinstalados": '{{$tcontabilidad[0]}}',
                 "instalados": '{{$tcontabilidad[1]}}',
                 "noprocede": '{{$tcontabilidad[2]}}'
            },
            {
                "year": "Pago",
                "noinstalados": '{{$tpago[0]}}',
                "instalados": '{{$tpago[1]}}',
                "noprocede": '{{$tpago[2]}}'
            }
            ,
            {
                "year": "Cobro",
                "noinstalados": '{{$tcobro[0]}}',
                "instalados": '{{$tcobro[1]}}',
                "noprocede": '{{$tcobro[2]}}'
            },
            {
                "year": "C. Bancaria",
                "noinstalados": '{{$tconciliacion_bancaria[0]}}',
                "instalados": '{{$tconciliacion_bancaria[1]}}',
                "noprocede": '{{$tconciliacion_bancaria[2]}}'
            },
            {
                "year": "Activo Fijo",
                "noinstalados": '{{$tactivo_fijo[0]}}',
                "instalados": '{{$tactivo_fijo[1]}}',
                "noprocede": '{{$tactivo_fijo[2]}}'
            },
            {
                "year": "Inventario",
                "noinstalados": '{{$tinventario[0]}}',
                "instalados": '{{$tinventario[1]}}',
                "noprocede": '{{$tinventario[2]}}'
            },
            {
                "year": "Utiles y Herramientas",
                "noinstalados": '{{$tutil_herramientas[0]}}',
                "instalados": '{{$tutil_herramientas[1]}}',
                "noprocede": '{{$tutil_herramientas[2]}}'
            },
            {
                "year": "Combustible",
                "noinstalados": '{{$tcombustible[0]}}',
                "instalados": '{{$tcombustible[1]}}',
                "noprocede": '{{$tcombustible[2]}}'
            },
            {
                "year": "Nomina",
                "noinstalados": '{{$tnomina[0]}}',
                "instalados": '{{$tnomina[1]}}',
                "noprocede": '{{$tnomina[2]}}'
            },
            {{--{--}}
                {{--"year": "Costo",--}}
                {{--"noinstalados": '{{$tcosto[0]}}',--}}
                {{--"instalados": '{{$tcosto[1]}}',--}}
                {{--"noprocede": '{{$tcosto[2]}}'--}}
            {{--},--}}
            {
                "year": "Venta",
                "noinstalados": '{{$tventa[0]}}',
                "instalados": '{{$tventa[1]}}',
                "noprocede": '{{$tventa[2]}}'
            },
            {
                "year": "C. Balance",
                "noinstalados": '{{$tconsolidador_balance[0]}}',
                "instalados": '{{$tconsolidador_balance[1]}}',
                "noprocede": '{{$tconsolidador_balance[2]}}'
            },
            {
                "year": "C. Cobros y Pagos",
                "noinstalados": '{{$tconsolidador_cobros_pagos[0]}}',
                "instalados": '{{$tconsolidador_cobros_pagos[1]}}',
                "noprocede": '{{$tconsolidador_cobros_pagos[2]}}'
            },
            {
                "year": "Estado Financiero",
                "noinstalados": '{{$testado_financiero[0]}}',
                "instalados": '{{$testado_financiero[1]}}',
                "noprocede": '{{$testado_financiero[2]}}'
            }
        ];

        AmCharts.ready(function () {
            // SERIAL CHART
            chart = new AmCharts.AmSerialChart();
            chart.dataProvider = chartData;
            chart.categoryField = "year";
            chart.plotAreaBorderAlpha = 0.2;

            // AXES
            // category
            var categoryAxis = chart.categoryAxis;
            categoryAxis.gridAlpha = 0.1;
            categoryAxis.axisAlpha = 0;
            categoryAxis.gridPosition = "start";

            // value
            var valueAxis = new AmCharts.ValueAxis();
            valueAxis.stackType = "regular";
            valueAxis.gridAlpha = 0.1;
            valueAxis.axisAlpha = 0;
            chart.addValueAxis(valueAxis);

            // GRAPHS
            // second graph
            graph = new AmCharts.AmGraph();
            graph.title = "No Instalados";
            graph.labelText = "[[value]]";
            graph.valueField = "noinstalados";
            graph.type = "column";
            graph.lineAlpha = 0;
            graph.fillAlphas = 1;
            graph.lineColor = "#dc3545";
            graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
            chart.addGraph(graph);

            // fourth graph
            graph = new AmCharts.AmGraph();
            graph.title = "Instalados";
            graph.labelText = "[[value]]";
            graph.valueField = "instalados";
            graph.type = "column";
            graph.lineAlpha = 0;
            graph.fillAlphas = 1;
            graph.lineColor = "#28a745";
            graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
            chart.addGraph(graph);

            // sixth graph
            graph = new AmCharts.AmGraph();
            graph.title = "No Procede";
            graph.labelText = "[[value]]";
            graph.valueField = "noprocede";
            graph.type = "column";
            graph.lineAlpha = 0;
            graph.fillAlphas = 1;
            graph.lineColor = "#17a2b8";
            graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span class='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
            chart.addGraph(graph);

            // LEGEND
            var legend = new AmCharts.AmLegend();
            legend.borderAlpha = 0.2;
            legend.horizontalGap = 10;
            chart.addLegend(legend);

            // WRITE
            chart.write("chartdiv");
        });

        // this method sets chart 2D/3D
        function setDepth() {
            if (document.getElementById("rb1").checked) {
                chart.depth3D = 0;
                chart.angle = 0;
            } else {
                chart.depth3D = 25;
                chart.angle = 30;
            }
            chart.validateNow();
        }
    </script>

    <div id="chartdiv" style="width: 1000px; height: 400px;"></div>
    <div style="margin-left:30px;">
        <input type="radio" checked="true" name="group" id="rb1" onclick="setDepth()">2D
        <input type="radio" name="group" id="rb2" onclick="setDepth()">3D
    </div>
