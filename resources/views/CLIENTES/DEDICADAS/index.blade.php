@extends('CLIENTES.plantilla2')
@section('contenido')
    <?php
    $ina=array();
    $lc=array();
    $lan=array();
    $vel=array();
    ?>
    @foreach ($trans as $tran)
        <?php
        $ina[$tran->ina]= $tran->ina;
        $lc[$tran->identificador]=$tran->identificador;
        $lan[$tran->lan]= $tran->lan;
        $vel[$tran->velocidad]=$tran->velocidad;
        ?>
    @endforeach
    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
    </nav>
    @if(!in_array(auth()->user()->UEB, config('constants.ueb')))
    {{Form::open(['Route'=> 'dedicadas.index', 'method'=>'GET', 'class'=>'form-inline pull-right'])}}
        <div class="form-group">
            {!!Form::select('ueb', array('Pinar del rio'=>'Pinar del Rio','Habana'=>'Habana','Villa Clara'=>'Villa Clara','Camaguey'=>'Camaguey','Oriente'=>'Oriente'), null, ['class'=>'form-control','placeholder'=>'Todos'])!!}
            {!! Form::button('<i class="fa fa-user-plus"></i>'.' Aceptar', ['type'=>'submit','class'=> 'btn btn-primary'] ) !!}
        </div>
    {{Form::close()}}
    @endif
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> DEDICADAS  @php echo count($trans) @endphp </strong>
        <strong class="fas fa-home">> NACIONAL  @php echo $nacional @endphp </strong>
         <strong class="fas fa-home">> INTERNACIONAL  @php echo $internacional @endphp </strong>
          <strong class="fas fa-home">> ALIMATIC  @php echo $alimatic @endphp </strong>
    </div>
    <table class="table table-hover table-striped table-sm">
        <thead>
        <tr>

            <th width="10px">Empresa</th>
            <th width="10px">Establecimiento</th>
            <th width="30px">Ina</th>
            <th width="30px">LC</th>
            <th width="20px">Lan</th>
            <th width="20px">Wan</th>
            <th width="20px">Velocidad</th>

        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>

                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->establecimiento}}</td>
                <td>{{$tran->ina}}</td>
                <td>{{$tran->identificador}}</td>
                <td>{{$tran->lan}}</td>
                <td>{{$tran->wan}}</td>
                <td>{{$tran->velocidad}}</td>

            </tr>
        @endforeach
        </tbody>
    </table>
    {!! $trans->render() !!}

    <script type="text/javascript">


        $(document).ready(function(){
            $('.modelo').combobox();
        });
    </script>
<script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
@endsection
