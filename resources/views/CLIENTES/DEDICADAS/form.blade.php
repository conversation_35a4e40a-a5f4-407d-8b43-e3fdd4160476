<div>
    {!! Form::hidden('cliente_id', $cliente, ['class'=> 'form-control']) !!}
</div>
<div>
    <strong> {!! Form::label('name', '*Establecimiento')!!}</strong>
    {!! Form::text('establecimiento', null, ['class'=> 'form-control']) !!}
</div>
<div>
    <strong> {!! Form::label('name', '*INA')!!}</strong>
    {!! Form::text('ina', null, ['class'=> 'form-control']) !!}
</div>
<div>
    <strong>{!! Form::label('name', '*Identificador')!!}</strong>
     {!! Form::text('identificador', null,['class'=> 'form-control'] ) !!}
</div>
<div>
    <strong>{!! Form::label('name', '*Lan')!!}</strong>
    {!! Form::text('lan', null,['class'=> 'form-control','type'=>'text','pattern'=>'[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}/[0-9]{2}','required','placeholder'=>'***********/00']) !!}
    {{--{!! Form::tel('no_movil', null, ['class'=> 'form-control','type'=>'tel','pattern'=>'[0-9]{8}', 'required']) !!}--}}
</div>
<div>
    <strong>{!! Form::label('name', '*Wan')!!}</strong>
    {!! Form::text('wan', null,['class'=> 'form-control','type'=>'text','pattern'=>'[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}/[0-9]{2}','required','placeholder'=>'***********/00'] ) !!}
</div>
<div>
 <strong>{!! Form::label('name', 'IP de Monitor de Servicio')!!}</strong>
     {!! Form::tel('ip_monitor', null,['class'=> 'form-control','type'=>'text','pattern'=>'[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}', 'placeholder'=>'***********'] ) !!}
</div>     
<div>
    <strong>{!! Form::label('name', '*Velocidad')!!}</strong>
    {!! Form::select('velocidad', array('64kbps'=>'64kbps','128kbps'=>'128kbps','256kbps'=>'256kbps','512kbps'=>'512kbps','1mbps'=>'1mbps','2mbps'=>'2mbps','4mbps'=>'4mbps','6mbps'=>'6mbps','8mbps'=>'8mbps','10mbps'=>'10mbps','12mbps'=>'12mbps','14mbps'=>'14mbps','16mbps'=>'16mbps','18mbps'=>'18mbps','20mbps'=>'20mbps','50mbps'=>'50mbps','80mbps'=>'80mbps'),null,['class'=> 'form-control'] ) !!}
</div>
<div>
    <strong>{!! Form::label('name', '*Acceso')!!}</strong>
    {!! Form::select('acceso', array('alimatic'=>'Alimatic','nacional'=>'Nacional','internacional'=>'Internacional'),null,['class'=> 'form-control'] ) !!}
</div>
<div>
    <strong>{!! Form::label('name', '*Código')!!}</strong>
    {!! Form::select('codigo_id', $codigosEnlaces , null,['class'=> 'form-control'] ) !!}
</div>
<div>
    <strong>{!! Form::label('name', 'Direccion')!!}</strong>
    {!! Form::text('direccion', null,['class'=> 'form-control','type'=>'text'] ) !!}
</div>
<div>
    <strong>{!! Form::label('name', 'Provincia')!!}</strong>
    {!! Form::select('provincia_id',$provincias, null,['class'=> 'form-control provincia','id'=>'provincia']) !!}
</div>
<div>
    <strong>{!! Form::label('name', 'Municipio')!!}</strong>
    {!! Form::select('municipio_id', ['placeholder'=>'Selecciona'],null,['class'=> 'form-control municipio','id'=>'municipio']) !!}
</div>
<div>
    <strong>{!! Form::label('name', 'Cantidad de Usuarios')!!}</strong>
    {!! Form::text('cant_usuarios', null,['class'=> 'form-control','type'=>'tel','pattern'=>'[0-9]*'] ) !!}
</div>
<div>
    <strong>{!! Form::label('name', 'Cantidad de Estaciones')!!}</strong>
    {!! Form::text('cant_pc', null,['class'=> 'form-control','type'=>'tel','pattern'=>'[0-9]*'] ) !!}
</div>
