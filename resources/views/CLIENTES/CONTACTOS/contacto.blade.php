</br>
<div class="alert alert-success">
    <strong>Contactos</strong>
    @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevocontacto"></button>
    @endif
</div>
@include ('CLIENTES.CONTACTOS.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped">
    <thead>
    <tr>
        <th width="10px">Nombre</th>
        <th width="10px">Cargo</th>
        <th width="30px">Teléfono</th>
        <th width="30px">Correo Electrónico</th>
    </tr>
    </thead>
    <tbody>
    @foreach($contactos as $contacto)
        <tr>
            <td>{{$contacto->nombre}}</td>
            <td>{{$contacto->cargo}}</td>
            <td>{{$contacto->telefono}}</td>
            <td>{{$contacto->correo}}</td>
            <td>
                <form action="{{Route('contactos.destroy', $contacto->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.CONTACTOS.modal_delete')
                </form>
            @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dcontacto{{$contacto->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#econtacto{{$contacto->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.CONTACTOS.modal_edit')
    @endforeach

    </tbody>
</table>
