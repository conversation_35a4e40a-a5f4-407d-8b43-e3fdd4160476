@extends('CLIENTES.plantilla')
@section('contenido')
@include ('CLIENTES.modal')
<?php
$enti=array();
$cont=array();
?>
@foreach ($trans as $tran)
    <?php
    $enti[$tran->entidad]= $tran->entidad;
    $cont[$tran->no_contrato]=$tran->no_contrato;
    ?>
@endforeach
    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
    </nav>
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
        <div class="alert alert-success">
                <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> CLIENTES</strong>
            @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevocliente"></button>
            @endif
    </div>
            <table class="table table-hover table-striped table-sm">
                <thead>
                <tr>
                    <th width="30px"></th>
                    <th width="300px">Empresa</th>
                    <th width="100px">Entidad</th>
                    <th width="20px">Provincia</th>
                    <th width="20px">Municipio</th>
                    <th width="20px">UEB ALIMATIC</th>
                    <th width="20px">Contrato</th>
                </tr>
                </thead>
                <tbody id="myTable">
                @foreach($trans as $tran)
                    <tr>
                        <td class="fab fa-asymmetrik"></td>
                        <td>{{$tran->empresa->nombre}}</td>
                        <td>{{$tran->entidad}}</td>
                        <td>{{$tran->provincia}}</td>
                        <td>{{$tran->municipio}}</td>
                        <td>{{$tran->uebalimatic}}</td>
                        <td>{{$tran->no_contrato}}</td>
                        <td width="15px">
                            {{--<a href="{{Route('clientes.show', $tran->id, $tran->nombre)}}" class="btn btn-success fas fa-eye float-right "></a>--}}
                            {{--<button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dcliente{{$tran->id}}"></button>--}}
                            {{--<button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#ecliente{{$tran->id}}"></button>--}}
                    </td>
                        <td>
                            <form action="{{Route('clientes.destroy', $tran->id)}}" method="post">
                                {{csrf_field()}}
                                <input type="hidden" name="_method" value="delete">
                                @include ('CLIENTES.modal_delete')
                            </form>
                            <td >
                            <a href="{{Route('clientes.show', $tran->id, $tran->empresa->nombre)}}" class="btn btn-success fas fa-eye float-right "></a>
                        </td>
			 @if(auth()->user()->admin_clientes=='1')
			<td width="5px">  <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#ecliente{{$tran->id}}"></button>
                        <td width="5px"> <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dcliente{{$tran->id}}"></button> </td>
                        @endif
                        </td>
                        <td>
                 
                        </td>
                   
                   </tr>
                      @include ('CLIENTES.modal_edit')
                @endforeach
                </tbody>
            </table>
    {!! $trans->render() !!}
    @include ('CLIENTES.FRAGMENTO.error')

<script type="text/javascript">
        //Desplegar combo para escoger empresa
    $(document).ready(function(){
        $('.modelo').combobox();
    });
    $(document).ready(function(){
        $('#modelo').combobox();
    });
</script>
  <script>
    //Buscar registros en la tabla entera
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
@endsection
