        @extends('CLIENTES.plantilla')
        @section('contenido')
        @include ('CLIENTES.ASIGNACIONE.modal')

        <nav class="navbar navbar-expand-sm border border-success navbar-dark ">
            <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
            <input class="form-control" id="myInput" type="text" placeholder="Buscar..">

        </nav>
    @include ('CLIENTES.FRAGMENTO.info') &nbsp;
    @include ('CLIENTES.FRAGMENTO.error') &nbsp;
    {!! $trans->render() !!}
    <br>
<a href="#abajo">Ultimo Registro</a>
 <a name="arriba"></a>
    <table class="table table-hover table-sm" id="mytabla">
        <thead>
            <tr>
                <th width="200px">Empresa</th>
                <th width="100px">Entidad</th>
                <th width="200px">Nombre</th>
                <th width="40px">Cargo</th>
                <th width="40px">Movil/IP</th>
                <th width="40px">ip</th>
                <th width="40px">Observaciones</th>
            </tr>
        </thead>
        <tbody id="myTable">
            @foreach($trans as $tran)
            <tr>
                @foreach($empre as $empresa)
                @if ($empresa->id === $tran->empresa_id)
                <td>{{$empresa->nombre}}</td>
                @endif

                @endforeach
                <td></a>{{$tran->entidad}}</td>
                <td>{{$tran->nombre}}</td>
                <td>{{$tran->cargo}}</td>
                <td>{{$tran->no_movil}}</td>
                <td>{{$tran->ip}}</td>
                <td>{{$tran->observacione}}</td>
                
                <td>
                    {!! Form::model($tran, ['route'=>['limpiar', $tran->id], 'method' => 'PUT'])!!}
                    <div class="hidden">
                        {!! csrf_field() !!}
                        @include ('CLIENTES.ASIGNACIONE.modal_delete')
                        </div>
                    {!! Form::close() !!}
                   @if(auth()->user()->UEB<>'invitado')
                </td>
                <td width="50px">
                    <button class="btn btn-danger  fas fa-angry float-right"  data-toggle="modal" data-target="#masig{{$tran->id}}"></button>
                </td>
                <td width="50px">
                    <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#easig{{$tran->id}}"></button>
                    @include ('CLIENTES.ASIGNACIONE.modal_edit')
                </td>
                <td width="50px">
                    <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dasig{{$tran->id}}"></button>
                </td>

                @endif
            
            @include ('CLIENTES.ASIGNACIONE.modal_mal')

            </tr>

            @endforeach

        </tbody>
    </table>
   <a name="abajo"></a>
   <a href="#arriba">Primer Registro</a>
       {!! $trans->render() !!}
    <script type="text/javascript">
        $(document).ready(function(){
            $('.modelo').combobox();
        });
        $(document).ready(function(){
            $('#modelo').combobox();
        });
    </script>
    <script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>

    @endsection
