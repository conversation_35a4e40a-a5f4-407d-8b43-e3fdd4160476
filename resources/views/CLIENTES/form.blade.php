
<?php
$provincias = array(
    "Pinar del Río" => "Pinar del Río",
    "Isla de la juventud" => "Isla de la juventud",
    "Artemisa" => "Artemisa",
    "La Habana" => "La Habana",
    "Mayabeque" => "Mayabeque",
    "Matanzas" => "Matanzas",
    "Villa Clara" => "Villa Clara",
    "Cienfuegos" => "Cienfuegos",
    "Sancti Spiritus" => "Sancti Spiritus",
    "Ciego de Avila" => "Ciego de Avila",
    "Camaguey" => "Camaguey",
    "Las Tunas" => "Las Tunas",
    "Holguín" => "<PERSON>lguín",
    "Granma" => "Granma",
    "Santiago de Cuba" => "Santiago de Cuba",
    "Guantánamo" => "Guantánamo",
);
$UEB = array(
    "Pinar del Rio" => "Pinar del Rio",
    "Habana" => "La habana",
    "Villa Clara" => "Villa Clara",
    "Camaguey" => "Camaguey",
    "Oriente" => "Oriente",
);
?>
<div>
     <strong> {!! Form::label('name', 'Empresa')!!}</strong>
    {!! Form::select('empresa_id' ,$empre, null, ['class'=> 'form-control','required']) !!}
</div>
<div>
    <strong> {!! Form::label('name', 'Entidad')!!}</strong>
    {!! Form::text('entidad', null, ['class'=> 'form-control', 'required']) !!}
</div>
<div>
    <strong> {!! Form::label('name', 'Direccion')!!}</strong>
    {!! Form::text('direccion', null, ['class'=> 'form-control', 'required']) !!}
</div>
<div>
    <strong> {!! Form::label('name', 'Provincia')!!}</strong>
    {!! Form::select('provincia',$provincias, null, ['class'=> 'form-control', 'required']) !!}
</div>
<div>
    <strong> {!! Form::label('name', 'No de Contrato')!!}</strong>
    {!! Form::text('no_contrato', null, ['class'=> 'form-control']) !!}
</div>
<div>
    <strong> {!! Form::label('name', 'Municipio')!!}</strong>
    {!! Form::text('municipio', null, ['class'=> 'form-control ', 'required']) !!}
</div>
@if(auth()->user()->UEB == 'Admin')
    <div>
        <strong> {!! Form::label('name', 'Ueb Alimatic')!!}</strong>
        {!! Form::select('uebalimatic',$UEB, null, ['class'=> 'form-control', 'required']) !!}
    </div>
@else()
<div>
    <strong> {!! Form::label('name', 'Ueb Alimatic')!!}</strong>
    {!! Form::label('uebalimatic', auth()->user()->UEB ,['class'=> 'form-control', 'required']) !!}
    {!! Form::hidden('uebalimatic', auth()->user()->UEB ,['class'=> 'form-control', 'required']) !!}
</div>
@endif
{{--Funcion de buscar en el combo box-se le agrega al control arriba el id de tipo modelo--}}

