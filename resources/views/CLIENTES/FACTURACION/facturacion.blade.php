@extends('CLIENTES.plantilla')
@section('contenido')

    <script src="../../amcharts/amcharts.js" type="text/javascript"></script>
    <script src="../../amcharts/serial.js" type="text/javascript"></script>

    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
        <div></div>
    </nav>
    <br>
    </br>

    <script>
        var chart;
        var legend;

        var chartData = [
                @php
                    $count=0;
                @endphp


        @foreach($clientes as $cliente)
                    {
                        "empresa": '{{$cliente->empresa->nombre}}',
                        "value": '{{$mydedicada[$count] + $mypaquete[$count]}}',
                        "short": '{{$mydedicada[$count]+ $mypaquete[$count]}}'
                    },
            @php
                $count++;
            @endphp
                @endforeach

        ];

        AmCharts.ready(function () {
            // SERIAL CHART
            var chart = new AmCharts.AmSerialChart();
            chart.dataProvider = chartData;
            chart.categoryField = "empresa";
            chart.startDuration = 2;
            // change balloon text color
            chart.balloon.color = "#000000";

            // AXES
            // category
            var categoryAxis = chart.categoryAxis;
            categoryAxis.gridAlpha = 0;
            categoryAxis.axisAlpha = 0;
            categoryAxis.labelsEnabled = false;

            // value
            var valueAxis = new AmCharts.ValueAxis();
            valueAxis.gridAlpha = 0;
            valueAxis.axisAlpha = 0;
            valueAxis.labelsEnabled = false;
            valueAxis.minimum = 0;
            chart.addValueAxis(valueAxis);

            // GRAPH
            var graph = new AmCharts.AmGraph();
            graph.balloonText = "[[category]]: [[value]]";
            graph.valueField = "value";
            graph.descriptionField = "short";
            graph.type = "column";
            graph.lineAlpha = 0;
            graph.fillAlphas = 1;
            graph.fillColors = ["#ffe78e", "#bf1c25"];
            graph.labelText = "[[description]]";
            graph.balloonText = "[[category]]: [[value]] Pesos";
            chart.addGraph(graph);
            chart.creditsPosition = "top-right";
            // WRITE
            chart.write("chartdiv");
        });
    </script>
    <div id="chartdiv" style="width: 1100px; height: 400px;"></div>

   <div class="alert alert alert-success text-xs font-weight-bold text-info text-uppercase mb-1 fa fa-dollar">Total a facturar:   {{$total_facturas}}</div>
    <table class="table table-hover table-striped">
        <thead>
        <tr>
            <td></td>
        </tr>
        </thead>
         <tbody id="myTable">
         @php
         $count=0;
         @endphp
         @foreach($clientes as $cliente)
             {{--<div class="alert alert-success container" id="#myTable">--}}
             <tr>
                 <th>
                     {{$cliente->empresa->nombre}}
                     ({{$cliente->entidad}})
                     <a href="{{Route('clientes.ver_factura', $cliente->id)}}" class="btn btn-outline-danger float-right "> $ {{$mydedicada[$count] + $mypaquete[$count] + $mysicema[$count] + $mydatadin[$count] }}</a>
                     @php
                         $count++;
                     @endphp
                 </th>
             </tr>
<div class="btn-group" role="group" aria-label="Basic example">



             {{--</div>--}}
         @endforeach
         </tbody>

<script>
$(document).ready(function(){
    $("#myInput").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $("#myTable tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});
</script>
@endsection
