</br>
<div class="alert alert-success">
    <strong>Audioconferencias Contratados</strong>
     @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevaaudioconferencia"></button>
    @endif
</div>
@include ('CLIENTES.AUDIOCONFERENCIA.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped table-sm">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Nombre del Canal</th>
        <th width="30px">Cantidad de Puntos</th>
    </tr>
    </thead>
    <tbody>
    @foreach($audioconferencias as $audioconferencia)
        <tr>
            <td>{{$audioconferencia->establecimiento}}</td>
            <td>{{$audioconferencia->nombre_canal}}</td>
            <td>{{$audioconferencia->cantidad_punto}}</td>
            <td>
                <form action="{{Route('audioconferencias.destroy', $audioconferencia->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.AUDIOCONFERENCIA.modal_delete')
                </form>
                 @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#daudio{{$audioconferencia->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#eaudio{{$audioconferencia->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.AUDIOCONFERENCIA.modal_edit')
    @endforeach
    </tbody>
</table>
