@extends('CLIENTES.plantilla')
@section('contenido')
    <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
    </nav>
    @include ('CLIENTES.FRAGMENTO.info')
    @include ('CLIENTES.CODIGOS.modal')
    @include ('CLIENTES.FRAGMENTO.error')

    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> CODIGOS</strong>
@if(auth()->user()->admin_clientes=='1')
        <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevocodigo"></button>
@endif
    </div>

    <table class="table table-hover table-striped">
        <thead>
        <tr>

            <th width="10px">Codigo</th>
            <th width="10px">Precio</th>
            <th width="30px">Descripción</th>
            <th width="30px">UEB</th>
        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>
                <td>{{$tran->codigo}}</td>
                <td>{{$tran->precio}}</td>
                <td>{{$tran->descripcion}}</td>
                <td>{{$tran->UEB}}</td>
                <td>
                    <form action="{{Route('codigos.destroy', $tran->id)}}" method="post">
                        {{csrf_field()}}
                        <input type="hidden" name="_method" value="delete">
                        @include ('CLIENTES.CODIGOS.modal_delete')
                    </form>
@if(auth()->user()->admin_clientes=='1')
                    <td width="5px">  <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#ecodigo{{$tran->id}}"></button>
	            <td width="5px"> <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dcodigo{{$tran->id}}"></button> </td>
@endif                
</td>
            </tr>
            @include ('CLIENTES.CODIGOS.modal_edit')

        @endforeach
        </tbody>
    </table>

<script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
@endsection
