@extends('CLIENTES.plantilla')
@section('contenido')
    <?php
    $canal=array();
    $cp=array();
    ?>
    @foreach ($trans as $tran)
    <?php
    $canal[$tran->nombre_canal]= $tran->nombre_canal;
    $cp[$tran->cantidad_punto]=$tran->cantidad_punto;
    ?>
    @endforeach
   <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
   </nav>
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> AUDIOCONFERENCIA  @php echo count($trans) @endphp </strong>
    </div>
    <table class="table table-hover table-striped table-sm">
        <thead>
        <tr>

            <th width="10px">Establecimiento</th>
            <th width="30px">Empresa</th>
            <th width="30px">Nombre del Canal</th>
            <th width="30px">Cantidad de Puntos</th>
        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>

                <td>{{$tran->establecimiento}}</td>
                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->nombre_canal}}</td>
                <td>{{$tran->cantidad_punto}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    {!! $trans->render() !!}
    {{--Funcion de buscar en el combo box-se le agrega al control arriba el id de tipo modelo--}}
    <script type="text/javascript">
        $(document).ready(function(){
            $('.modelo').combobox();
        });
    </script>
<script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>

@endsection