</br>
<div class="alert alert-success">
    <strong>TEMPORALMENTE EN CONSTRUCCION</strong>
     @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevodatadin"></button>
    @endif
</div>
@include ('CLIENTES.DATADIN.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped table-sm">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Código</th>
        <th width="30px">Descripcion</th>
    </tr>
    </thead>
    <tbody>
    @foreach($datadins as $datadin)
        <tr>
            <td>{{$datadin->establecimiento}}</td>
            <td>{{$datadin->codigo->codigo}}</td>
            <td>{{$datadin->descripcion}}</td>
            <td>
                <form action="{{Route('datadins.destroy', $datadin->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.DATADIN.modal_delete')
                </form>
                 @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#ddata{{$datadin->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#edata{{$datadin->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.DATADIN.modal_edit')
    @endforeach
    </tbody>
</table>
