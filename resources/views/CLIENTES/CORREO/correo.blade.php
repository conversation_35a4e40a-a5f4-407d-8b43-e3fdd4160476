</br>
<div class="alert alert-success">
    <strong>Co<PERSON>os <PERSON></strong>
     @if(auth()->user()->admin_clientes=='1')
    <button class="btn btn-success btn-link fas fa-plus-circle float-right " data-toggle="modal" data-target="#nuevocorreo"></button>
    @endif
</div>
@include ('CLIENTES.CORREO.modal')
{{--@include ('CLIENTES.FRAGMENTO.info')--}}
<table class="table table-hover table-striped table-sm">
    <thead>
    <tr>
        <th width="10px">Establecimiento</th>
        <th width="10px">Usuario</th>
        <th width="30px">Tipo de Correo</th>
        <th width="30px">Acceso</th>
    </tr>
    </thead>
    <tbody>
    @foreach($correos as $correo)
        <tr>
            <td>{{$correo->establecimiento}}</td>
            <td>{{$correo->usuario}}</td>
            <td>{{$correo->tipo_correo}}</td>
            <td>{{$correo->acceso}}</td>
            <td>
                <form action="{{Route('correos.destroy', $correo->id)}}" method="post">
                    {{csrf_field()}}
                    <input type="hidden" name="_method" value="delete">
                    @include ('CLIENTES.CORREO.modal_delete')
                </form>
               @if(auth()->user()->admin_clientes=='1')
                <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#dcorreo{{$correo->id}}"></button>
                <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#ecorreo{{$correo->id}}"></button>
                @endif
            </td>
        </tr>
        @include ('CLIENTES.CORREO.modal_edit')
    @endforeach

    </tbody>
</table>
