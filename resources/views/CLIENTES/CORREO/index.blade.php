@extends('CLIENTES.plantilla')
@section('contenido')
    <?php
    $usu=array();
    $tipo=array();
    ?>
    @foreach ($trans as $tran)
        <?php
        $usu[$tran->usuario]= $tran->usuario;
        $tipo[$tran->tipo_correo]=$tran->tipo_correo;
        ?>
    @endforeach
   <nav class="navbar navbar-expand-sm border border-success navbar-dark">
        <a href="{{Route('home')}}"> <img src="img/logo.png" alt="Logo" style="width:120px;"></a>
        <input class="form-control" id="myInput" type="text" placeholder="Buscar..">
   </nav>
    @include ('CLIENTES.FRAGMENTO.info')
    <br>
     {{Form::open(['Route'=> 'correos.index', 'method'=>'GET', 'class'=>'form-inline pull-right'])}}
    <div class="form-group d-flex pb-3" style="gap: 10px;">
        <strong>{!!Form::label('Tipo de Correo ')!!}</strong>
        {!!Form::select('tipo_correo', array('personal'=>'personal','corporativo'=>'corporativo'), null, ['class'=>'form-control','placeholder'=>'Todos'])!!}
         <strong>{!!Form::label('UEB')!!}</strong>

         @if(!in_array(auth()->user()->UEB, config('constants.ueb')))
            {!!Form::select('ueb', array('Pinar del rio'=>'Pinar del Rio','Habana'=>'Habana','Villa Clara'=>'Villa Clara','Camaguey'=>'Camaguey','Oriente'=>'Oriente'), null, ['class'=>'form-control','placeholder'=>'Todos'])!!}
         @endif
            {!! Form::button('<i class="fa fa-user-plus"></i>'.' Aceptar', ['type'=>'submit','class'=> 'btn btn-primary'] ) !!}
    </div>
    {{Form::close()}}


    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> CORREOS  @php echo count($trans) @endphp </strong>
    </div>
    <table class="table table-hover table-striped table-sm">
        <thead>
        <tr>

            <th width="30px">Empresa</th>
            <th width="30px">Usuario</th>
            <th width="20px">Tipo de Correo</th>
            <th width="20px">Acceso</th>
        </tr>
        </thead>
        <tbody id="myTable">
        @foreach($trans as $tran)
            <tr>

                @foreach($empre as $empresa)
                    @if ($empresa->id === $tran->cliente->empresa_id)
                        <td>{{$empresa->nombre}}</td>
                    @endif
                @endforeach
                <td>{{$tran->usuario}}</td>
                <td>{{$tran->tipo_correo}}</td>
                <td>{{$tran->acceso}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    {!! $trans->render() !!}
    {{--Funcion de buscar en el combo box-se le agrega al control arriba el id de tipo modelo--}}
    <script type="text/javascript">
        $(document).ready(function(){
            $('.modelo').combobox();
        });
    </script>
<script>
        $(document).ready(function(){
            $("#myInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#myTable tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
@endsection
