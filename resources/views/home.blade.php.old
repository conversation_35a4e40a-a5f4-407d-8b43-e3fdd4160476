@extends('CLIENTES.plantilla')

@section('contenido')
    <script src="amcharts/amcharts.js" type="text/javascript"></script>
    <script src="amcharts/serial.js" type="text/javascript"></script>
    @include ('CLIENTES.FRAGMENTO.info')
    <style type="text/css">
        #opaco { opacity:0.5; }
        #borde {
            border-left-width: 5px;
        }
    </style>
    <script src="{{ asset('js/app.js') }}" defer></script>
    <nav class="navbar navbar-expand-md navbar-light navbar-laravel">
        <div class="container">
            <a class="navbar-brand" href="{{ url('/') }}">
                {{ config('app.name', 'Laravel') }}
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <!-- Left Side Of Navbar -->
                <ul class="navbar-nav mr-auto">

                </ul>

                <!-- Right Side Of Navbar -->
                <ul class="navbar-nav ml-auto">
                    <!-- Authentication Links -->
                    @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                    </li>
                    @if (Route::has('register'))
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
                        </li>
                    @endif
                    @else
                        <li class="nav-item dropdown">
                            <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                {{ Auth::user()->name }} <span class="caret"></span>
                            </a>

                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                <a class="dropdown-item" href="{{ route('logout') }}"
                                   onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                    {{ __('Salir') }}
                                </a>

                                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                    @csrf
                                </form>
                            </div>
                        </li>
                        @endguest
                </ul>
            </div>
        </div>
    </nav>
    <div class="row">

        <div class="col-md-3 float-left card border-info" id="borde">
            <div class="card-header">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Listado de Servicios</div>
            </div>
            <div class="card-body alert alert-info">
             <div class="text-xs font-weight-bold fas fa-mobile-alt text-info text-uppercase mb-1"><a class="text-info" href="{{Route('apns.index')}}"> &nbsp;&nbsp;APN <span class="badge badge-primary">{{$apn}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-project-diagram text-uppercase mb-1"><a class="text-info"href="{{Route('dedicadas.index')}}"> Dedicadas  <span class="badge badge-primary">{{$dedicada}}</span></a></div>
                <div class="border border-info "></div>
                <div class="text-xs font-weight-bold text-info fas fa-blender-phone text-uppercase mb-1"><a class="text-info"href="{{Route('conmutadas.index')}}"> Conmutadas</a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-envelope-open-text text-uppercase mb-1"><a class="text-info"href="{{Route('correos.index')}}"> Correo  <span class="badge badge-primary">{{$correo}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fab fa-internet-explorer text-uppercase mb-1"><a class="text-info"href="{{Route('navegacions.index')}}"> Navegacion  <span class="badge badge-primary">{{$navegacion}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-h-square text-uppercase mb-1"><a class="text-info"href="{{Route('hostings.index')}}"> &nbsp;Hosting  <span class="badge badge-primary">{{$hosting}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-microphone-slash text-uppercase mb-1"><a class="text-info"href="{{Route('audioconferencias.index')}}">Audio Conf  <span class="badge badge-primary">{{$audioconferencia}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-cloud-upload-alt text-uppercase mb-1"><a class="text-info"href="{{Route('nubes.index')}}">&nbsp;Nube  <span class="badge badge-primary">{{$nube}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-clipboard-list text-uppercase mb-1"><a class="text-info"href="{{Route('dominios.index')}}">&nbsp;&nbsp;Dominio  <span class="badge badge-primary">{{$dominio}}</span></a></div>
                <div class="border border-info"></div>
                <div class="text-xs font-weight-bold text-info fas fa-clipboard-list text-uppercase mb-1"><a class="text-info"href="{{Route('solicitudes.index')}}">&nbsp;&nbsp;Solicitudes  <span class="badge badge-primary">{{$solicitude}}</span></a></div>
                <div class="border border-info"></div>
            </div>
        </div>
        <div>&nbsp;</div>
        <div>&nbsp;</div>
        <div>&nbsp;</div>
        <div>&nbsp;</div>


        <div class="col-md-8 card border-info float-right" id="borde">
            <div class="card-header">
                <div class="col-md-8 text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-archway"> Bienvenidos</div>
                @if(auth()->user()->UEB=='Admin')
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-archway"><a class="text-info" href="{{ route('users.index') }}"> Administrar Roles</a> </div>
                @endif
            </div>
            <div class="card-body">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-list-alt"> <a class="text-info" href="{{route('clientes.index')}}"> Mostrar listado de Clientes.</a></div>
                @if(auth()->user()->facturador=='1')
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-list-alt float-right"> <a class="text-info" href="{{route('codigos.index')}}">Tabla de Codigos</a></div>
	
		@endif
  		 @if(auth()->user()->admin_ip=='1')
   
		 <div class="text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-list-alt float-right"> <a class="text-info" href="{{route('ips.index')}}">Tabla de Asignaciones de IP</a></div>
                @endif
		  
                
               	

                {{--<div class="text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-list-alt"> <a class="text-info" href="{{route('clientes.facturacion')}}"> Facturacion</a></div>--}}

                <img src="img/conexiones.png " alt="" style='height:100%; width:100%; object-fit: contain'/>

            </div>

       @if(auth()->user()->facturador=='1')
                {!! Form::open(['route'=> 'clientes.facturacion', 'method'=>'POST'])!!}
                {{csrf_field()}}
                <div class="col-md-4 text-xs font-weight-bold text-info text-uppercase mb-1 ">
                    {!! Form::hidden('ueb', auth()->user()->UEB , null,['class'=> 'form-control'] ) !!}
                </div>
                <div class="col-md-4 text-xs font-weight-bold text-info text-uppercase mb-1 ">
                    {!! Form::button('<i class="fa fa-dollar"></i>'.'Facturar', ['type'=>'submit','class'=> 'btn btn-outline-info'] ) !!}
                </div>
            {{--<div class="col-md-4 text-xs font-weight-bold text-info text-uppercase mb-1 fas fa-archway"> UEB a Facturar</div>--}}
                {!! Form::close() !!}
            </div>
            </div>
    </div>

        @endif
    </div>
    <div class="col-md-12 text-center">
        &nbsp;
    </div>
    <div class="col-md-12 text-center">
        <div class="fas fa-copyright text-info text-uppercase mb-1"> ALIMATIC</div>
    </div>


    <script>
        var chart;
        var chartData = [
            {
                "servicio": "APN",
                "valor": {{$apn}},
                "color": "#FF0F00"
            },
            {
                "servicio": "Dedicada",
                "valor": {{$dedicada}},
                "color": "#FF6600"
            },
            {
                "servicio": "Correo",
                "valor": {{$correo}},
                "color": "#FF9E01"
            },
            {
                "servicio": "Navegacion",
                "valor": {{$navegacion}},
                "color": "#FCD202"
            },
                {
                    "servicio": "Hosting",
                    "valor": {{$hosting}},
                    "color": "#F8FF01"
                },
                {
                    "servicio": "Audio Cnf",
                    "valor": {{$audioconferencia}},
                    "color": "#B0DE09"
                },
                {
                    "servicio": "Nube",
                    "valor": {{$nube}},
                    "color": "#04D215"
                },
                {
                    "servicio": "Dominio",
                    "valor": {{$dominio}},
                    "color": "#0D8ECF"
                }
//                {
//                    "servicio": "Netherlands",
//                    "valor": 665,
//                    "color": "#0D52D1"
//                },
//                {
//                    "servicio": "Russia",
//                    "valor": 580,
//                    "color": "#2A0CD0"
//                },
//                {
//                    "servicio": "South Korea",
//                    "valor": 443,
//                    "color": "#8A0CCF"
//                },
//                {
//                    "servicio": "Canada",
//                    "valor": 441,
//                    "color": "#CD0D74"
//                }
        ];
        AmCharts.ready(function () {
            // SERIAL CHART
            chart = new AmCharts.AmSerialChart();
            chart.dataProvider = chartData;
            chart.categoryField = "servicio";
            chart.startDuration = 1;
            chart.depth3D = 50;
            chart.angle = 30;
            chart.marginRight = -45;

            // AXES
            // category
            var categoryAxis = chart.categoryAxis;
            categoryAxis.gridAlpha = 0;
            categoryAxis.axisAlpha = 0;
            categoryAxis.gridPosition = "start";

            // value
            var valueAxis = new AmCharts.ValueAxis();
            valueAxis.axisAlpha = 0;
            valueAxis.gridAlpha = 0;
            chart.addValueAxis(valueAxis);

            // GRAPH
            var graph = new AmCharts.AmGraph();
            graph.valueField = "valor";
            graph.colorField = "color";
            graph.balloonText = "<b>[[category]]: [[value]]</b>";
            graph.type = "column";
            graph.lineAlpha = 0.5;
            graph.lineColor = "#FFFFFF";
            graph.topRadius = 1;
            graph.fillAlphas = 0.9;
            chart.addGraph(graph);

            // CURSOR
            var chartCursor = new AmCharts.ChartCursor();
            chartCursor.cursorAlpha = 0;
            chartCursor.zoomable = false;
            chartCursor.categoryBalloonEnabled = false;
            chartCursor.valueLineEnabled = true;
            chartCursor.valueLineBalloonEnabled = true;
            chartCursor.valueLineAlpha = 1;
            chart.addChartCursor(chartCursor);

            chart.creditsPosition = "top-right";

            // WRITE
            chart.write("chartdiv");
        });
    </script>

    </div>
    <body >
    <div class="container-fluid" id="chartdiv" style="width: 50%; height: 300px;"></div>
    </body>
@endsection
