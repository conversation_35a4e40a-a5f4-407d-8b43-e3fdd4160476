@extends('CLIENTES.plantilla')
@section('contenido')

    @include ('CLIENTES.FRAGMENTO.info')
    <br>
    <div class="alert alert-success">
        <strong><a class="fas fa-home text-info" href="{{Route('home')}}"></a> USUARIOS</strong>
    </div>
    <table class="table table-hover table-striped">
        <thead>
        <tr>
            <th width="10px">ID</th>
            <th width="30px">name</th>
            <th width="30px">email</th>
            {{--<th width="20px">remember_token</th>--}}
            <th width="20px">UEB</th>
            <th width="20px">Facturador</th>
            <th width="20px">Admin Cliente</th>
            <th width="20px">Admin IP</th>
        </tr>
        </thead>
        <tbody>
        @foreach($trans as $tran)
            <tr>
                <td>{{$tran->id}}</td>
                <td>{{$tran->name}}</td>
                <td>{{$tran->email}}</td>
                <td>{{$tran->UEB}}</td>
                <td>@if ($tran->facturador== 1)Si @else No @endif </td>
                <td>@if ($tran->admin_clientes== 1)Si @else No @endif </td>
                <td>@if ($tran->admin_ip== 1)Si @else No @endif</td>
                <td>
                    <form action="{{Route('users.destroy', $tran->id)}}" method="post">
                        {{csrf_field()}}
                        <input type="hidden" name="_method" value="delete">
                        @include ('auth.modal_delete')
                    </form>
                    <button class="btn btn-danger  fas fa-minus-circle float-right " data-toggle="modal" data-target="#d{{$tran->id}}"></button>
                    <button class="btn btn-info  fas fa-edit float-right " data-toggle="modal" data-target="#e{{$tran->id}}"></button>
                    <button class="btn btn-info  fas fa-key float-right " data-toggle="modal" data-target="#r{{$tran->id}}"></button>
                </td>
            </tr>
            @include ('auth.modal_edit')
            @include ('auth.modal_reset_password')
        @endforeach
        </tbody>
    </table>
@endsection
