<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CrearTablaSicema extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sicema', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cliente_id')->unsigned();
            $table->foreign('cliente_id')->references('id')->on('clientes')->onUpdate('cascade')->onDelete('cascade');
            $table->string('establecimiento',100);
            $table->enum('contabilidad',array('si','no','no procede'));
            $table->enum('pago',array('si','no','no procede'));
            $table->enum('cobro',array('si','no','no procede'));
            $table->enum('conciliacion_bancaria',array('si','no','no procede'));
            $table->enum('activo_fijo',array('si','no','no procede'));
            $table->enum('inventario',array('si','no','no procede'));
            $table->enum('util_herramientas',array('si','no','no procede'));
            $table->enum('combustible',array('si','no','no procede'));
            $table->enum('nomina',array('si','no','no procede'));
            $table->enum('costo',array('si','no','no procede'));
            $table->enum('venta',array('si','no','no procede'));
            $table->enum('consolidador_balance',array('si','no','no procede'));
            $table->enum('consolidador_cobros_pagos',array('si','no','no procede'));
            $table->enum('estado_financiero',array('si','no','no procede'));
            $table->enum('recursos_humanos',array('si','no','no procede'));
            $table->string('version_ejecutable',200);
            $table->integer('version_bd');
            $table->string('codigo',200)->nullable();
            $table->integer('precio')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sicema');
    }
}
