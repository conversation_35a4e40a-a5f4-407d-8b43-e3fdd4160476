<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CrearTablaDedicada extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dedicadas', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cliente_id')->unsigned();
            $table->foreign('cliente_id')->references('id')->on('clientes')->onUpdate('cascade')->onDelete('cascade');
            $table->string('establecimiento',100);
            $table->string('ina',100);
            $table->string('identificador',100);
            $table->string('lan',200);
            $table->string('wan',200);
            $table->enum('velocidad',array('64kbps','128kbps','256kbps','512kbps','1mbps','2mbps','4mbps','6mbps','8mbps','10mbps','12mbps','14mbps','16mbps','18mbps','20mbps','80mbps'));
            $table->integer('codigo_id')->unsigned();
            $table->foreign('codigo_id')->references('id')->on('codigos')->onUpdate('cascade')->onDelete('cascade')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dedicadas');
    }
}
