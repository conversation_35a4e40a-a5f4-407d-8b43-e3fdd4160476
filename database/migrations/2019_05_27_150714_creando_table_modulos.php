<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreandoTableModulos extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('modulos', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('cliente_id')->unsigned();
            $table->foreign('cliente_id')->references('id')->on('clientes')->onUpdate('cascade')->onDelete('cascade');
            $table->string('establecimiento',100);
            $table->string('version_ejecutable',200);
            $table->integer('version_bd');
            $table->enum('identificador',array('contabilidad','finanza','inventario','catagolo','utiles y herramientas','combustible','ventas','estado financiero','sicobal','nomina','recursos humanos','datadim','mantenimiento','contratos','transporte','acopio de leche'));
            $table->string('codigo',200)->nullable();
            $table->integer('precio')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('modulos');
    }
}
