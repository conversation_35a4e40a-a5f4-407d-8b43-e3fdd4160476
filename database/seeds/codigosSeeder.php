<?php
namespace Database\Seeds;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class codigosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS = 0;');
        DB::table('codigos')->truncate();
        DB::table('codigos')->insert([
            'codigo'=>'vacio',
            'precio'=>'0',
            'descripcion'=>'Sin codigo'
        ]);
        
        DB::table('codigos')->insert([
            'codigo'=>'01.001',
            'precio'=>'210',
            'descripcion'=>'Enlace 64 Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.002',
            'precio'=>'700',
            'descripcion'=>'Enlace 64 Kbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.003',
            'precio'=>'315',
            'descripcion'=>'Enlace 128 Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.004',
            'precio'=>'1050',
            'descripcion'=>'Enlace 128 Kbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.005',
            'precio'=>'420',
            'descripcion'=>'Enlace 256 Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.006',
            'precio'=>'1400',
            'descripcion'=>'Enlace 256 Kbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.007',
            'precio'=>'560',
            'descripcion'=>'Enlace 512 Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.008',
            'precio'=>'2520',
            'descripcion'=>'Enlace 512 Kbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.009',
            'precio'=>'945',
            'descripcion'=>'Enlace 1 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.010',
            'precio'=>'5005',
            'descripcion'=>'Enlace 1 Mbps Internacional'
        ]);
       DB::table('codigos')->insert([
            'codigo'=>'01.011',
            'precio'=>'1855',
            'descripcion'=>'Enlace 2 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.012',
            'precio'=>'9975',
            'descripcion'=>'Enlace 2 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.013',
            'precio'=>'3605',
            'descripcion'=>'Enlace 4 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.014',
            'precio'=>'19950',
            'descripcion'=>'Enlace 4 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.015',
            'precio'=>'6895',
            'descripcion'=>'Enlace 6 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.016',
            'precio'=>'22960',
            'descripcion'=>'Enlace 6 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.017',
            'precio'=>'7210',
            'descripcion'=>'Enlace 8 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.018',
            'precio'=>'38955',
            'descripcion'=>'Enlace 8 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.019',
            'precio'=>'7805',
            'descripcion'=>'Enlace 10 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.020',
            'precio'=>'39445',
            'descripcion'=>'Enlace 10 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.021',
            'precio'=>'8470',
            'descripcion'=>'Enlace 12 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.022',
            'precio'=>'40740',
            'descripcion'=>'Enlace 12 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.023',
            'precio'=>'9660',
            'descripcion'=>'Enlace 14 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.024',
            'precio'=>'41465',
            'descripcion'=>'Enlace 14 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.025',
            'precio'=>'10640',
            'descripcion'=>'Enlace 16 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.026',
            'precio'=>'42840',
            'descripcion'=>'Enlace 16 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.027',
            'precio'=>'11550',
            'descripcion'=>'Enlace 18 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.028',
            'precio'=>'46690',
            'descripcion'=>'Enlace 18 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.029',
            'precio'=>'12005',
            'descripcion'=>'Enlace 20 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.030',
            'precio'=>'49490',
            'descripcion'=>'Enlace 20 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.031',
            'precio'=>'13160',
            'descripcion'=>'Enlace 22 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.032',
            'precio'=>'53445',
            'descripcion'=>'Enlace 22 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.033',
            'precio'=>'14140',
            'descripcion'=>'Enlace 24 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.034',
            'precio'=>'58450',
            'descripcion'=>'Enlace 24 Mbps Internacional'
        ]);
           DB::table('codigos')->insert([
            'codigo'=>'01.035',
            'precio'=>'14840',
            'descripcion'=>'Enlace 26 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.036',
            'precio'=>'63455',
            'descripcion'=>'Enlace 26 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.037',
            'precio'=>'16555',
            'descripcion'=>'Enlace 28 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.038',
            'precio'=>'66150',
            'descripcion'=>'Enlace 28 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.039',
            'precio'=>'17920',
            'descripcion'=>'Enlace 30 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.040',
            'precio'=>'67200',
            'descripcion'=>'Enlace 30 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.041',
            'precio'=>'19040',
            'descripcion'=>'Enlace 32 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.042',
            'precio'=>'68460',
            'descripcion'=>'Enlace 32 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.043',
            'precio'=>'19320',
            'descripcion'=>'Enlace 34 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.044',
            'precio'=>'74130',
            'descripcion'=>'Enlace 34 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.045',
            'precio'=>'22050',
            'descripcion'=>'Enlace 40 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.046',
            'precio'=>'86520',
            'descripcion'=>'Enlace 40 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.047',
            'precio'=>'24815',
            'descripcion'=>'Enlace 50 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.048',
            'precio'=>'98840',
            'descripcion'=>'Enlace 50 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.049',
            'precio'=>'27580',
            'descripcion'=>'Enlace 80 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.050',
            'precio'=>'111230',
            'descripcion'=>'Enlace 80 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.051',
            'precio'=>'30345',
            'descripcion'=>'Enlace 100 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.052',
            'precio'=>'123585',
            'descripcion'=>'Enlace 100 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.053',
            'precio'=>'33110',
            'descripcion'=>'Enlace 128 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.054',
            'precio'=>'135940',
            'descripcion'=>'Enlace 128 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.055',
            'precio'=>'35840',
            'descripcion'=>'Enlace 150 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.056',
            'precio'=>'148295',
            'descripcion'=>'Enlace 150 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.057',
            'precio'=>'38605',
            'descripcion'=>'Enlace 256 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.058',
            'precio'=>'160650',
            'descripcion'=>'Enlace 256 Mbps Internacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.059',
            'precio'=>'41370',
            'descripcion'=>'Enlace 512 Mbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.060',
            'precio'=>'173005',
            'descripcion'=>'Enlace 512 Mbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.061',
            'precio'=>'44100',
            'descripcion'=>'Enlace 1 Gbps Kbps Nacional'
        ]);
        DB::table('codigos')->insert([
            'codigo'=>'01.062',
            'precio'=>'185360',
            'descripcion'=>'Enlace 512 Gbps Internacional'
        ]);
         DB::table('codigos')->insert([
            'codigo'=>'01.063',
            'precio'=>'910',
            'descripcion'=>'Cuota de Instalacion de 64kbps hasta 2048kbps'
        ]);
          DB::table('codigos')->insert([
            'codigo'=>'01.064',
            'precio'=>'1820',
            'descripcion'=>'Cuota de Instalacion de 4mbps hasta 34mbps'
        ]);
          DB::table('codigos')->insert([
            'codigo'=>'01.065',
            'precio'=>'3640',
            'descripcion'=>'Cuota de Instalacion de 40mbps hasta 1gbps'
        ]);
    

    }
}
