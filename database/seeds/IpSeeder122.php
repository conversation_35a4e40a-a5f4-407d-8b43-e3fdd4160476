<?php
namespace Database\Seeds;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IpSeeder122 extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS = 0;');
        DB::table('asignaciones')->truncate();
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'***************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************01']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************02']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************03']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************04']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************05']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************06']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************07']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************08']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************09']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************10']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************11']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************12']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************13']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************14']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************15']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************16']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************17']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************18']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************19']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************20']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************21']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************22']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************23']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************24']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************25']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************26']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************27']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************28']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************29']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************30']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************31']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************32']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************33']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************34']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************35']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************36']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************37']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************38']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************39']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************40']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************41']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************42']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************43']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************44']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************45']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************46']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************47']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************48']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************49']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************50']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************51']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************52']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************53']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************54']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************55']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************56']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************57']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************58']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************59']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************60']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************61']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************62']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************63']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************64']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************65']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************66']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************67']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************68']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************69']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************70']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************71']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************72']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************73']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************74']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************75']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************76']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************77']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************78']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************79']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************80']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************81']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************82']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************83']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************84']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************85']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************86']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************87']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************88']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************89']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************90']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************91']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************92']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************93']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************94']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************95']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************122']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************97']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************98']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************99']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************00']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************01']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************02']);
        DB::table('asignaciones')->insert([
            'ip'=>'***************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************04']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************05']);
        DB::table('asignaciones')->insert([
            'ip'=>'***************']);
        DB::table('asignaciones')->insert([
            'ip'=>'***************']);
        DB::table('asignaciones')->insert([
            'ip'=>'***************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************09']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************10']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************11']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************12']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************13']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************14']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************15']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************16']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************17']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************18']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************19']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************20']);
        DB::table('asignaciones')->insert([
            'ip'=>'***************']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************22']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************23']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************24']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************25']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************26']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************27']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************28']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************29']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************30']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************31']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************32']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************33']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************34']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************35']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************36']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************37']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************38']);
        DB::table('asignaciones')->insert([
            'ip'=>'*************39']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************4']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************5']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************6']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************7']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************8']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************9']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************0']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************1']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************2']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************3']);
        DB::table('asignaciones')->insert([
            'ip'=>'**************4']);
    }
}
