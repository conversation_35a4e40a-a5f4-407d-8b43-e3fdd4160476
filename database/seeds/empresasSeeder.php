<?php
namespace Database\Seeds;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class empresasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
       DB::statement('SET FOREIGN_KEY_CHECKS = 0;');
       DB::table('empresas')->truncate();
       DB::statement('SET FOREIGN_KEY_CHECKS = 1;');
       // Pinar del Rio
       DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Pinar del Río'
       ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos y Confitería Pinar del Rio'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos de Pinar del Río'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de La Coloma (EPICOL)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera de Pinar del Río (PESCARIO)'
        ]);

        //Artemisa

        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Artemisa'
        ]);

        //Habana

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Carnico Tauro'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Carnica Habana '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Coopelia'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Complejo Lácteo Habana '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Torrefacción y Comercialización del Café “Cubacafé”'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Conserva de Vegetales'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Aseguramiento de la Industria Láctea'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Aseguramiento de la Industria Carnica'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Habana'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Mayabeque'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Cervecería Guido Pérez'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Aceites y Grasas Comestibles Habana'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Confitería y Derivados de la Harina'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cubana del Pan'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cubana de Molinería '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Logística y Aseguramiento de la Industria Cervecería, Bebidas y Refrescos'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Lanchera Flota del Golfo (Flogolfo)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Desarrollo de Tecnologías Acuicolas, EDTA'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa para el  Cultivo del Camarón'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa PRODAL'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Proveedora del MINAL, PROPES'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Comercial de Alimentos COPMAR'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Proyectos, Construcciones y Servicios Navales del MINAL, CEPRONA'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Comercial CARIBEX'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa PESCACARIBE'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Terminal Refrigerada TERREF '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Transporte Refrigerado (ATLAS)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Sistemas Automatizados (ALIMATIC)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Servicios Varios del MINAL, (EMSERVA)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Construcciones Metálicas y Eléctricas (COMELEC)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Diseño y Servicios de Ingeniería'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Importadora  Exportadora del MINAL, ALIMPEX'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Refrigeración y Calderas del MINAL'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Seguridad y Protección MINAL, ESEP'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Revista MAR Y PESCA'
        ]);

        //Mayabeque

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de Batabanó (PESCAHABANA)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera ACUABANA'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Carnica Nueva Paz'
        ]);

        // Matanzas

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Matanzas'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Matanzas '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquerade  Matanzas (PESCAMAT)'
        ]);

        //Cienfuegos

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Cienfuegos'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos  ESCAMBRAY'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de Cienfuegos (EPICIEN)'
        ]);

        // Villa Clara

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Villa Clara'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Villa Clara'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Villa Clara '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Cervecería "Antonio Días Santana" Manacas'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial Caibarien (EPICAI)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa  Pesquera de Villa Clara (PESCAVILLA)'
        ]);

        // S Spiritus

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Sancti Spíritus'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos  Río Zaza '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial Sacnti Spíritus (EPISAN)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera de Sancti Spíritus (PESCASPIR)'
        ]);

        // Ciego A

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Ciego de Ávila'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos  Ciego de Ávila'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Ciego de Ávila '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de Ciego de Ávila (EPIVILA)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera de Ciego de Ávila (PESCAVILA)'
        ]);

        // Camaguey

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Camaguey'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos  Camaguey'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Camaguey '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Cervecería Tínima '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Aceites y Grasas Comestibles Camaguey'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de Santa Cruz del Sur (EPISUR)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera de Camaguey (PESCACAM)'
        ]);

        // Tunas

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Las Tunas'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lacteos Las Tunas'
        ]);

        // Holguin

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Holguín'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Holguín'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empesa Pesquera de Holguín (PESCAHOL) '
        ]);

        // Granma

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Granma'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Bayamo'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Granma '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de Niquero (EPINIQ)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial de Granma (EPIGRAN)'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa  Pesquera Granma (PESCAGRAN)'
        ]);

        // Santiago

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Santiago de Cuba'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Santiago de Cuba'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Bebidas y Refrescos Santiago de Cuba '
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Cervecería Santiago de Cuba”Hatuey”'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Refinadora de Aceites Santiago de Cuba'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Procesadora de Soya, PDS'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera de Santiago de Cuba (PESCASAN)'
        ]);

        //Guantanamo

        DB::table('empresas')->insert([
            'nombre'=>'Empresa Cárnica Guantánamo'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa de Productos Lácteos Guantánamo'
        ]);

        // Isla J

        DB::table('empresas')->insert([
            'nombre'=>'Empresa  Productora de Alimentos Isla de la Juventud'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'Empresa Pesquera Industrial Isla de la Juventud (PESCAISLA)'
        ]);

        // Presupuestadas

        DB::table('empresas')->insert([
            'nombre'=>'Centro de Investigaciones Pesqueras CIP'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'ONIE'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'STELLA'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'EMPIASA'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'MINAL'
        ]);
        DB::table('empresas')->insert([
            'nombre'=>'CORALSA'
        ]);
    }
}
