<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/serial.js" type="text/javascript"></script>
        <script src="../amcharts/gantt.js" type="text/javascript"></script>

        <script>

        var chart = AmCharts.makeChart("chartdiv", {
            "type": "gantt",
            "period": "hh",
            "valueAxis": {
                "type": "date"
            },
            "brightnessStep": 10,
            "graph": {
                "fillAlphas": 1,
                "dateFormat":"JJ:NN",
                "balloonText":"[[open]] - [[value]]"
            },
            "rotate": true,
            "dataDateFormat":"YYYY-MM-DD",
            "categoryField": "category",
            "segmentsField": "segments",
            "startDate": "2015-01-01",
            "startField": "start",
            "endField": "end",
            "durationField": "duration",
            "dataProvider": [{
                "category": "John",
                "segments": [{
                    "start": 0,
                    "duration": 2
                }, {
                    "duration": 2
                }, {
                    "duration": 2
                }]
            }, {
                "category": "Smith",
                "segments": [{
                    "start": 3,
                    "duration": 2
                }, {
                    "duration": 1
                }, {
                    "duration": 4
                }]
            }, {
                "category": "Ben",
                "segments": [{
                    "start": 5,
                    "duration": 2
                }, {
                    "start": 9,
                    "duration": 2
                }]
            } ],
            "chartCursor": {
                "valueBalloonsEnabled": false,
                "cursorAlpha": 0,
                "valueLineBalloonEnabled": true,
                "valueLineEnabled": true,
                "valueZoomable":true,
                "zoomable":false
            },

            "valueScrollbar": {
                "position":"top",
                "autoGridCount":true,
                "color":"#000000"
            }
        })
        </script>
    </head>

    <body>
        <div id="chartdiv" style="width: 100%; height: 400px;"></div>
    </body>

</html>