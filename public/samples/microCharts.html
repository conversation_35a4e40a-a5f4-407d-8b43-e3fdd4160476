<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
		<script src="../amcharts/serial.js" type="text/javascript"></script>
		<script src="../amcharts/pie.js" type="text/javascript"></script>

        <script>

			AmCharts.ready(function () {

			    // line chart, with a bullet at the end
			    var chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "day": 1,
			            "value": 120
			    }, {
			        "day": 2,
			            "value": 124
			    }, {
			        "day": 3,
			            "value": 127
			    }, {
			        "day": 4,
			            "value": 122
			    }, {
			        "day": 5,
			            "value": 121
			    }, {
			        "day": 6,
			            "value": 123
			    }, {
			        "day": 7,
			            "value": 118
			    }, {
			        "day": 8,
			            "value": 113
			    }, {
			        "day": 9,
			            "value": 122
			    }, {
			        "day": 10,
			            "value": 125,
			        bullet: "round"
			    }];
			    chart.categoryField = "day";
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 5;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    var graph = new AmCharts.AmGraph();
			    graph.valueField = "value";
			    graph.bulletField = "bullet";
			    graph.showBalloon = false;
			    graph.lineColor = "#a9ec49";
			    chart.addGraph(graph);

			    var valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    chart.addValueAxis(valueAxis);

			    var categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;
			    categoryAxis.startOnAxis = true;
			    chart.write("line1");


			    // small column chart
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "day": 1,
			            "value": -5
			    }, {
			        "day": 2,
			            "value": 3
			    }, {
			        "day": 3,
			            "value": 7
			    }, {
			        "day": 4,
			            "value": -3
			    }, {
			        "day": 5,
			            "value": 3
			    }, {
			        "day": 6,
			            "value": 4
			    }, {
			        "day": 7,
			            "value": 6
			    }, {
			        "day": 8,
			            "value": -3
			    }, {
			        "day": 9,
			            "value": -2
			    }, {
			        "day": 10,
			            "value": 6
			    }];
			    chart.categoryField = "day";
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 0;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "value";
			    graph.type = "column";
			    graph.fillAlphas = 1;
			    graph.lineColor = "#a9ec49";
			    graph.showBalloon = false;
			    chart.addGraph(graph);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;
			    chart.write("column1");


			    // line chart, with different line color below zero
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "day": 1,
			            "value": 120
			    }, {
			        "day": 2,
			            "value": 54
			    }, {
			        "day": 3,
			            "value": -18
			    }, {
			        "day": 4,
			            "value": -12
			    }, {
			        "day": 5,
			            "value": -51
			    }, {
			        "day": 6,
			            "value": 12
			    }, {
			        "day": 7,
			            "value": 56
			    }, {
			        "day": 8,
			            "value": 113
			    }, {
			        "day": 9,
			            "value": 142
			    }, {
			        "day": 10,
			            "value": 125
			    }];
			    chart.categoryField = "day";
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 5;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "value";
			    graph.showBalloon = false;
			    graph.lineColor = "#ffbf63";
			    graph.negativeLineColor = "#289eaf";
			    chart.addGraph(graph);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;
			    categoryAxis.startOnAxis = true;

			    // using guide to show 0 grid
			    var guide = new AmCharts.Guide();
			    guide.value = 0;
			    guide.lineAlpha = 0.1;
			    valueAxis.addGuide(guide);
			    chart.write("line2");


			    // column chart with different negative colors
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "day": 1,
			            "value": -5
			    }, {
			        "day": 2,
			            "value": 3
			    }, {
			        "day": 3,
			            "value": 7
			    }, {
			        "day": 4,
			            "value": -3
			    }, {
			        "day": 5,
			            "value": 3
			    }, {
			        "day": 6,
			            "value": 4
			    }, {
			        "day": 7,
			            "value": 6
			    }, {
			        "day": 8,
			            "value": -3
			    }, {
			        "day": 9,
			            "value": -2
			    }, {
			        "day": 10,
			            "value": 6
			    }];
			    chart.categoryField = "day";
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 0;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "value";
			    graph.type = "column";
			    graph.fillAlphas = 1;
			    graph.showBalloon = false;
			    graph.lineColor = "#ffbf63";
			    graph.negativeFillColors = "#289eaf";
			    graph.negativeLineColor = "#289eaf";
			    chart.addGraph(graph);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;
			    chart.write("column2");


			    // column chart (thermometer)
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        x: 1,
			        y: 100
			    }];
			    chart.categoryField = "x";
			    chart.rotate = true;
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 0;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "y";
			    graph.type = "column";
			    graph.fillAlphas = 1;
			    graph.fillColors = ["#49cbec", "#f73838"];
			    graph.gradientOrientation = "horizontal";
			    graph.lineColor = "#FFFFFF";
			    graph.showBalloon = false;
			    chart.addGraph(graph);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    valueAxis.stackType = "100%"; // this is set to achieve that column would occupie 100% of the chart area
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;

			    // guide to show position
			    guide = new AmCharts.Guide();
			    guide.value = 66;
			    guide.lineAlpha = 1;
			    guide.above = true;
			    valueAxis.addGuide(guide);
			    chart.write("thermometer1");



			    // column chart (thermometer)
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "x": 1,
			            "y": 100
			    }];
			    chart.categoryField = "x";
			    chart.rotate = true;
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 0;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "y";
			    graph.type = "column";
			    graph.fillAlphas = 1;
			    graph.fillColors = ["#49cbec", "#f73838"];
			    graph.gradientOrientation = "horizontal";
			    graph.lineColor = "#FFFFFF";
			    graph.showBalloon = false;
			    chart.addGraph(graph);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    valueAxis.stackType = "100%"; // this is set to achieve that column would occupie 100% of the chart area
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;

			    // guide to show position
			    guide = new AmCharts.Guide();
			    guide.value = 29;
			    guide.lineAlpha = 1;
			    guide.above = true;
			    valueAxis.addGuide(guide);
			    chart.write("thermometer2");




			    // percent chart
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "x": 1,
			            "y1": 66,
			            "y2": 34
			    }];
			    chart.categoryField = "x";
			    chart.rotate = true;
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 0;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "y1";
			    graph.type = "column";
			    graph.fillAlphas = 0.6;
			    graph.fillColors = "#000000";
			    graph.gradientOrientation = "horizontal";
			    graph.lineColor = "#FFFFFF";
			    graph.showBalloon = false;
			    chart.addGraph(graph);

			    var graph2 = new AmCharts.AmGraph();
			    graph2.valueField = "y2";
			    graph2.type = "column";
			    graph2.fillAlphas = 0.2;
			    graph2.fillColors = "#000000";
			    graph2.lineColor = "#FFFFFF";
			    graph2.showBalloon = false;
			    chart.addGraph(graph2);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    valueAxis.stackType = "100%"; // this is set to achieve that column would occupie 100% of the chart area
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;

			    chart.write("percent1");


			    // percent chart
			    chart = new AmCharts.AmSerialChart();
			    chart.dataProvider = [{
			        "x": 1,
			            "y1": 29,
			            "y2": 71
			    }];
			    chart.categoryField = "x";
			    chart.rotate = true;
			    chart.autoMargins = false;
			    chart.marginLeft = 0;
			    chart.marginRight = 0;
			    chart.marginTop = 0;
			    chart.marginBottom = 0;

			    graph = new AmCharts.AmGraph();
			    graph.valueField = "y1";
			    graph.type = "column";
			    graph.fillAlphas = 0.6;
			    graph.fillColors = "#000000";
			    graph.gradientOrientation = "horizontal";
			    graph.lineColor = "#FFFFFF";
			    graph.showBalloon = false;
			    chart.addGraph(graph);

			    graph2 = new AmCharts.AmGraph();
			    graph2.valueField = "y2";
			    graph2.type = "column";
			    graph2.fillAlphas = 0.2;
			    graph2.fillColors = "#000000";
			    graph2.lineColor = "#FFFFFF";
			    graph2.showBalloon = false;
			    chart.addGraph(graph2);

			    valueAxis = new AmCharts.ValueAxis();
			    valueAxis.gridAlpha = 0;
			    valueAxis.axisAlpha = 0;
			    valueAxis.stackType = "100%"; // this is set to achieve that column would occupie 100% of the chart area
			    chart.addValueAxis(valueAxis);

			    categoryAxis = chart.categoryAxis;
			    categoryAxis.gridAlpha = 0;
			    categoryAxis.axisAlpha = 0;
			    chart.write("percent2");

			    // small pie chart
			    chart = new AmCharts.AmPieChart();
			    chart.dataProvider = [{
			        "x": 1,
			            "value": 29
			    }, {
			        "x": 2,
			            "value": 71
			    }];
			    chart.labelField = "x";
			    chart.valueField = "value";
			    chart.labelsEnabled = false;
			    chart.balloonText = undefined;
			    chart.valueText = undefined;
			    chart.radius = 10; // half of a width of a div
			    chart.colors = ["#DADADA", "#777777"];
			    chart.startDuration = 0;
			    chart.write("pie1");

			    // small pie chart
			    chart = new AmCharts.AmPieChart();
			    chart.dataProvider = [{
			        "x": 1,
			            "value": 45
			    }, {
			        "x": 2,
			            "value": 55
			    }];
			    chart.labelField = "x";
			    chart.balloonText = undefined;
			    chart.valueField = "value";
			    chart.labelsEnabled = false;
			    chart.valueText = undefined;
			    chart.radius = 10;
			    chart.colors = ["#DADADA", "#777777"];
			    chart.startDuration = 0;
			    chart.write("pie2");

			});

        </script>
    </head>

    <body>

    <!-- first chart group -->
    <div class="chart-block">
      <p><b>Some simple sparklines and micro column chart:</b></p>
      Alaska, 120 <div id="line1" style="vertical-align: middle; display: inline-block; width: 100px; height: 30px;"></div> 6% <div id="column1" style="vertical-align: middle;display: inline-block; width: 110px; height: 30px;"></div>
    </div>

    <!-- second chart group -->
    <div class="chart-block">
      <p><b>More advanced, with different line and fill colors:</b></p>
      Alaska, 120 <div id="line2" style="vertical-align: middle; display: inline-block; width: 100px; height: 30px;"></div> 6% <div id="column2" style="vertical-align: middle;display: inline-block; width: 110px; height: 30px;"></div>
    </div>

    <!-- third chart group -->
    <div class="chart-block">
      <p><b>Thermometer example:</b></p>
      Alaska, 66% <div id="thermometer1" style="vertical-align: middle; display: inline-block; width: 100px; height: 20px;"></div><br />
      Alabama, 39% <div id="thermometer2" style="vertical-align: middle;display: inline-block; width: 100px; height: 20px"></div>
    </div>

    <!-- fourth chart group -->
    <div class="chart-block">
      <p><b>Simple percent chart example:</b></p>
      Alaska, 66% <div id="percent1" style="vertical-align: middle; display: inline-block; width: 100px; height: 20px;"></div><br />
      Alabama, 39% <div id="percent2" style="vertical-align: middle;display: inline-block; width: 100px; height: 20px;"></div>
    </div>

    <!-- fifth chart group -->
    <div class="chart-block">
      <p><b>Simple percent chart example:</b></p>
      Alaska, 71% <div id="pie1" style="vertical-align: middle; display: inline-block; width: 20px; height: 20px;"></div><br />
      Alabama, 45% <div id="pie2" style="vertical-align: middle; display: inline-block; width: 20px; height: 20px;"></div>
    </div>

    <p>Please note that sparkline charts are NOT usable with the free version of JavaScript Charts due to branding link taking up most of the plot area. Please consider <a href="http://shop.amcharts.com/">acquiring a commercial license</a>. (licenses starting as low as USD 140)</p>

</html>