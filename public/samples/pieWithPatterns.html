<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/pie.js" type="text/javascript"></script>

        <script>
            var chart;

            var chartData = [{
                "country": "Czech Republic",
                "litres": 301.9,
                "pattern": {
                    "url": "patterns/black/pattern1.png",
                    "width": 4,
                    "height": 4,
                    "color": "#cc0000"
                }
            }, {
                "country": "Ireland",
                "litres": 201.1,
                "pattern": {
                    "url": "patterns/black/pattern2.png",
                    "width": 4,
                    "height": 4
                }
            }, {
                "country": "Germany",
                "litres": 165.8,
                "pattern": {
                    "url": "patterns/black/pattern3.png",
                    "width": 4,
                    "height": 4
                }
            }, {
                "country": "Australia",
                "litres": 139.9,
                "pattern": {
                    "url": "patterns/black/pattern4.png",
                    "width": 4,
                    "height": 4
                }
            }, {
                "country": "Austria",
                "litres": 128.3,
                "pattern": {
                    "url": "patterns/black/pattern5.png",
                    "width": 4,
                    "height": 4
                }
            }, {
                "country": "UK",
                "litres": 99,
                "pattern": {
                    "url": "patterns/black/pattern6.png",
                    "width": 4,
                    "height": 4
                }
            }, {
                "country": "Belgium",
                "litres": 60,
                "pattern": {
                    "url": "patterns/black/pattern7.png",
                    "width": 4,
                    "height": 4
                }
            }];

            AmCharts.ready(function() {
                // PIE CHART
                chart = new AmCharts.AmPieChart();

                chart.dataProvider = chartData;
                chart.titleField = "country";
                chart.valueField = "litres";
                chart.patternField = "pattern";
                chart.outlineColor = "#000000";
                chart.outlineAlpha = 0.6;
                chart.balloonText = "[[title]]<br><span style='font-size:14px'><b>[[value]]</b> ([[percents]]%)</span>";

                var legend = new AmCharts.AmLegend();
                legend.markerBorderColor = "#000000";
                legend.switchType = undefined;
                legend.align = "center";
                chart.addLegend(legend);

                // WRITE
                chart.write("chartdiv");
            });
        </script>
    </head>

    <body>
        <div id="chartdiv" style="width: 100%; height: 400px;"></div>
    </body>

</html>