<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>amCharts Responsive Example</title>
  <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
  <script src="http://www.amcharts.com/lib/3/pie.js"></script>
  <script src="../responsive.min.js"></script>
  <style>
  body, html {
    height: 100%;
    padding: 0;
    margin: 0;
  }
  </style>
  <script>
    AmCharts.makeChart("chartdiv", {
      "type": "pie",
      "dataProvider": [{
        "country": "Czech Republic",
        "litres": 156.9
      }, {
        "country": "Ireland",
        "litres": 131.1
      }, {
        "country": "Germany",
        "litres": 115.8
      }, {
        "country": "Australia",
        "litres": 109.9
      }, {
        "country": "Austria",
        "litres": 108.3
      }, {
        "country": "UK",
        "litres": 65
      }, {
        "country": "Belgium",
        "litres": 50
      }],
      "titleField": "country",
      "valueField": "litres",
      "balloonText": "[[title]]<br><span style='font-size:14px'><b>[[value]]</b> ([[percents]]%)</span>",
      "innerRadius": "30%",
      "legend": {
        "align": "center",
        "markerType": "circle"
      },
      "responsive": {
        "enabled": true,
        "addDefaultRules": true,
        "rules": [
          {
            "minWidth": 500,
            "overrides": {
              "innerRadius": "50%",
            }
          }
        ]
      }
    });
  </script>
</head>

<body>
  <div id="chartdiv" style="width: 100%; height: 100%;"></div>
</body>

</html>