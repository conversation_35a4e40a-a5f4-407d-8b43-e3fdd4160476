<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
  
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Responsive Example</title>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/radar.js"></script>
    <script src="../responsive.min.js"></script>
    <style>
    body, html {
        height: 100%;
        padding: 0;
        margin: 0;
    }
    </style>
    <script>
      var chart = AmCharts.makeChart("chartdiv", {
          "type": "radar",
          "dataProvider": [{
            "country": "Czech Republic",
            "litres": 156.9
          }, {
            "country": "Ireland",
            "litres": 131.1
          }, {
            "country": "Germany",
            "litres": 115.8
          }, {
            "country": "Australia",
            "litres": 109.9
          }, {
            "country": "Austria",
            "litres": 108.3
          }, {
            "country": "UK",
            "litres": 99
          }],
          "categoryField": "country",
          "startDuration": 2,
          "valueAxes": [{
            "axisAlpha": 0.15,
            "minimum": 0,
            "dashLength": 3,
            "axisTitleOffset": 20,
            "gridCount": 5
          }],
          "graphs": [{
            "valueField": "litres",
            "title": "Litres",
            "bullet": "round",
            "balloonText": "[[value]] litres of beer per year"
          }],
          "legend": {},
          "responsive": {
            "enabled": true
          }
      });
    </script>
  </head>
  
  <body>
    <div id="chartdiv" style="width: 100%; height: 100%;"></div>
  </body>

</html>