<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>amCharts Data Loader Example</title>
  <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
  <script src="http://www.amcharts.com/lib/3/gauge.js"></script>
  <script src="../dataloader.min.js"></script>
  <style>
  body, html {
    font-family: Verdana;
    font-size: 12px;
  }
  #chartdiv {
    width: 100%;
    height: 500px;
  }
  </style>
  <script>
    var chart = AmCharts.makeChart( "chartdiv", {
      "type": "gauge",
      "startDuration": 0.1,
      "dataLoader": {
        "url": "data/gauge.json"
      },
      "axes": [ {
        "id": "axis1",
        "axisAlpha": 0,
        "endAngle": 360,
        "endValue": 12,
        "minorTickInterval": 0.2,
        "showFirstLabel": false,
        "startAngle": 0,
        "topTextYOffset": 100,
        "valueInterval": 1
      }, {
        "id": "axis2",
        "axisAlpha": 0,
        "endAngle": 360,
        "endValue": 60,
        "radius": 60,
        "showFirstLabel": false,
        "startAngle": 0,
        "valueInterval": 5,
        "labelFrequency": 0,
        "tickLength": 10
      } ]
    } );
  </script>
</head>

<body>
  <div id="chartdiv"></div>
</body>

</html>