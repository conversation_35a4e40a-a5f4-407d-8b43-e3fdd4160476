<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>amCharts Data Loader Example</title>
  <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
  <script src="http://www.amcharts.com/lib/3/pie.js"></script>
  <script src="../dataloader.min.js"></script>
  <style>
  body, html {
    font-family: Verdana;
    font-size: 12px;
  }
  #chartdiv {
    width: 100%;
    height: 500px;
  }
  </style>
  <script>
    AmCharts.makeChart("chartdiv", {
      "type": "pie",
      "dataLoader": {
        "url": "data/pie.csv",
        "format": "csv",
        "delimiter": ",",
        "useColumnNames": true
     },
      "titleField": "country",
      "valueField": "litres",
      "balloonText": "[[title]]<br><span style='font-size:14px'><b>[[value]]</b> ([[percents]]%)</span>",
      "innerRadius": "30%",
      "legend": {
        "align": "center",
        "markerType": "circle"
      },
      "responsive": {
        "enabled": true,
        "addDefaultRules": true,
        "rules": [
          {
            "minWidth": 500,
            "overrides": {
              "innerRadius": "50%",
            }
          }
        ]
      }
    });
  </script>
</head>

<body>
  <div id="chartdiv"></div>
</body>

</html>