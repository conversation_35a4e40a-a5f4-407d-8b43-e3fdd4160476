<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Data Loader Example</title>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/serial.js"></script>
    <script src="http://www.amcharts.com/lib/3/amstock.js"></script>
    <script src="../dataloader.min.js"></script>

    <!-- jQuery UI resources used for progress bar -->
    <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
    <script src="http://code.jquery.com/jquery-1.10.2.js"></script>
    <script src="http://code.jquery.com/ui/1.11.4/jquery-ui.js"></script>

    <style>
    body, html {
      font-family: Verdana;
      font-size: 12px;
      background: #000;
    }
    #chartcontainer, #chartdiv {
      width: 100%;
      height: 500px;
      position: relative;
    }
    #chartdiv .amChartsPeriodSelector {
      padding-left: 35px;
    }
    .amChartsButtonSelected {
      font-weight: bold;
    }
    .amChartsButton {
      color: #fff;
      background: transparent;
      opacity: 0.7;
      border: 1px solid rgba(255,255,2555,.3);
      -moz-border-radius: 5px;
      border-radius: 5px;
      margin: 1px;
      outline: none;
      box-sizing: border-box;
    }
    .amChartsInputField {
      color: #000;
    }
    .ui-progressbar {
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 50%;
      margin-left: -150px;
      width: 300px;
      text-align: center;
    }
    .progress-label {
      position: absolute;
      left: 50%;
      top: 4px;
      margin-left: -15px;
      font-weight: bold;
      text-shadow: 1px 1px 0 #fff;
    }
    </style>
    <script>
    var chart = AmCharts.makeChart("chartdiv", {
      "type": "stock",
      "color": "#fff",
      "dataSets": [{
      "title": "MSFT",
        "fieldMappings": [{
          "fromField": "Open",
          "toField": "open"
        }, {
          "fromField": "High",
          "toField": "high"
        }, {
          "fromField": "Low",
          "toField": "low"
        }, {
          "fromField": "Close",
          "toField": "close"
        }, {
          "fromField": "Volume",
          "toField": "volume"
        }],
        "compared": false,
        "categoryField": "Date",

        /**
         * data loader for data set data
         */
        "dataLoader": {
          "url": "data/MSFT.csv",
          "format": "csv",
          "showCurtain": false,
          "showErrors": true,
          "async": true,
          "reverse": true,
          "delimiter": ",",
          "useColumnNames": true,
          "progress": udpateProgress
       },

       /**
        * data loader for events data
        */
        "eventDataLoader": {
          "url": "data/MSFT_events.csv",
          "format": "csv",
          "showCurtain": false,
          "showErrors": true,
          "async": true,
          "reverse": true,
          "delimiter": ",",
          "useColumnNames": true,
          "progress": udpateProgress,
          "postProcess": function ( data ) {
            for ( var x in data ) {
              switch( data[x].Type ) {
                case 'A':
                  var color = "#85CDE6";
                  break;
                default:
                  var color = "#cccccc";
                  break;
              }
              data[x].Description = data[x].Description.replace( "Upgrade", "<strong style=\"color: #0c0\">Upgrade</strong>" ).replace( "Downgrade", "<strong style=\"color: #c00\">Downgrade</strong>" );
              data[x] = {
                type: "pin",
                graph: "g1",
                backgroundColor: color,
                date: data[x].Date,
                text: data[x].Type,
                description: "<strong>" + data[x].Title + "</strong><br />" + data[x].Description
              };
            }
            return data;
          }
       }

      }, {
      "title": "TXN",
        "fieldMappings": [{
          "fromField": "Open",
          "toField": "open"
        }, {
          "fromField": "High",
          "toField": "high"
        }, {
          "fromField": "Low",
          "toField": "low"
        }, {
          "fromField": "Close",
          "toField": "close"
        }, {
          "fromField": "Volume",
          "toField": "volume"
        }],
        "compared": true,
        "categoryField": "Date",
        "dataLoader": {
          "url": "data/TXN.csv",
          "format": "csv",
          "showCurtain": false,
          "showErrors": true,
          "async": true,
          "reverse": true,
          "delimiter": ",",
          "useColumnNames": true,
          "progress": udpateProgress
       }
      }],
      "dataDateFormat": "YYYY-MM-DD",

      "panels": [{
          "title": "Value",
          "percentHeight": 70,

          "stockGraphs": [{
            "type": "candlestick",
            "id": "g1",
            "openField": "open",
            "closeField": "close",
            "highField": "high",
            "lowField": "low",
            "valueField": "close",
            "lineColor": "#fff",
            "fillColors": "#fff",
            "negativeLineColor": "#db4c3c",
            "negativeFillColors": "#db4c3c",
            "fillAlphas": 1,
            "comparedGraphLineThickness": 2,
            "columnWidth": 0.7,
            "useDataSetColors": false,
            "comparable": true,
            "compareField": "close",
            "showBalloon": false,
            "proCandlesticks": true
          }],

          "stockLegend": {
            "valueTextRegular": undefined,
            "periodValueTextComparing": "[[percents.value.close]]%"
          }

        },

        {
          "title": "Volume",
          "percentHeight": 30,
          "marginTop": 1,
          "columnWidth": 0.6,
          "showCategoryAxis": false,

          "stockGraphs": [{
            "valueField": "volume",
            "openField": "open",
            "type": "column",
            "showBalloon": false,
            "fillAlphas": 1,
            "lineColor": "#fff",
            "fillColors": "#fff",
            "negativeLineColor": "#db4c3c",
            "negativeFillColors": "#db4c3c",
            "useDataSetColors": false
          }],

          "stockLegend": {
            "markerType": "none",
            "markerSize": 0,
            "labelText": "",
            "periodValueTextRegular": "[[value.close]]"
          },

          "valueAxes": [{
            "usePrefixes": true
          }]
        }
      ],

      "listeners": [{
        "event": "dataUpdated",
        "method": function() {
          progressbar.hide();
        }
      }],

      "panelsSettings": {
        "color": "#fff",
        "plotAreaFillColors": "#333",
        "plotAreaFillAlphas": 1,
        "marginLeft": 60,
        "marginTop": 5,
        "marginBottom": 5
      },

      "chartScrollbarSettings": {
        "graph": "g1",
        "graphType": "line",
        "usePeriod": "WW",
        "backgroundColor": "#333",
        "graphFillColor": "#666",
        "graphFillAlpha": 0.5,
        "gridColor": "#555",
        "gridAlpha": 1,
        "selectedBackgroundColor": "#444",
        "selectedGraphFillAlpha": 1
      },

      "categoryAxesSettings": {
        "equalSpacing": true,
        "gridColor": "#555",
        "gridAlpha": 1
      },

      "valueAxesSettings": {
        "gridColor": "#555",
        "gridAlpha": 1,
        "inside": false,
        "showLastLabel": true
      },

      "chartCursorSettings": {
        "pan": true,
        "valueLineEnabled": true,
        "valueLineBalloonEnabled": true
      },

      "legendSettings": {
        "color": "#fff"
      },

      "stockEventsSettings": {
        "showAt": "high",
        "type": "pin"
      },

      "balloon": {
        "textAlign": "left",
        "offsetY": 10
      },

      "periodSelector": {
        "position": "bottom",
        "periods": [{
          "period": "DD",
          "count": 10,
          "label": "10D"
        }, {
          "period": "MM",
          "count": 1,
          "label": "1M"
        }, {
          "period": "MM",
          "count": 6,
          "label": "6M"
        }, {
          "period": "YYYY",
          "count": 1,
          "label": "1Y"
        }, {
          "period": "YYYY",
          "count": 2,
          "selected": true,
          "label": "2Y"
        }, {
          "period": "YTD",
          "label": "YTD"
        }, {
          "period": "MAX",
          "label": "MAX"
        }]
      }
    });
    
    var progressbar, progressLabel;
    function udpateProgress( totalPercent, filePercent, url ) {

      // initialize progress bar
      if ( progressbar === undefined ) {
        progressbar = $( "#progressbar" ).show();
        progressLabel = $( ".progress-label" );
        progressbar.progressbar( {
          value: false,
          change: function() {
            progressLabel.text( progressbar.progressbar( "value" ) + "%" );
          },
          complete: function() {
            progressLabel.text( "100%" );
          }
        } );
      }

      // update value
      progressbar.progressbar( "value", totalPercent );
    }

    </script>
  </head>

  <body>
    <div id="chartcontainer">
      <div id="chartdiv"></div>
      <div id="progressbar" style="display: none;"><div class="progress-label">...</div></div>
    </div>
  </body>

</html>