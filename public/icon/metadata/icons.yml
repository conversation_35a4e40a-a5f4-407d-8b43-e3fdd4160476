500px:
  changes:
    - '4.4'
    - 5.0.0
  label: 500px
  search:
    terms: []
  styles:
    - brands
  unicode: f26e
accessible-icon:
  changes:
    - 5.0.0
  label: Accessible Icon
  search:
    terms:
      - accessibility
      - handicap
      - person
      - wheelchair
      - wheelchair-alt
  styles:
    - brands
  unicode: f368
accusoft:
  changes:
    - 5.0.0
  label: Accusoft
  search:
    terms: []
  styles:
    - brands
  unicode: f369
acquisitions-incorporated:
  changes:
    - 5.4.0
  label: Acquisitions Incorporated
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - game
      - gaming
      - tabletop
  styles:
    - brands
  unicode: f6af
ad:
  changes:
    - 5.3.0
  label: Ad
  search:
    terms: []
  styles:
    - solid
  unicode: f641
address-book:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Address Book
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2b9
address-card:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Address Card
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2bb
adjust:
  changes:
    - '1'
    - 5.0.0
  label: adjust
  search:
    terms:
      - contrast
  styles:
    - solid
  unicode: f042
adn:
  changes:
    - '3.2'
    - 5.0.0
  label: App.net
  search:
    terms: []
  styles:
    - brands
  unicode: f170
adversal:
  changes:
    - 5.0.0
  label: Adversal
  search:
    terms: []
  styles:
    - brands
  unicode: f36a
affiliatetheme:
  changes:
    - 5.0.0
  label: affiliatetheme
  search:
    terms: []
  styles:
    - brands
  unicode: f36b
air-freshener:
  changes:
    - 5.2.0
  label: Air Freshener
  search:
    terms: []
  styles:
    - solid
  unicode: f5d0
algolia:
  changes:
    - 5.0.0
  label: Algolia
  search:
    terms: []
  styles:
    - brands
  unicode: f36c
align-center:
  changes:
    - '1'
    - 5.0.0
  label: align-center
  search:
    terms:
      - middle
      - text
  styles:
    - solid
  unicode: f037
align-justify:
  changes:
    - '1'
    - 5.0.0
  label: align-justify
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f039
align-left:
  changes:
    - '1'
    - 5.0.0
  label: align-left
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f036
align-right:
  changes:
    - '1'
    - 5.0.0
  label: align-right
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f038
alipay:
  changes:
    - 5.3.0
  label: Alipay
  search:
    terms: []
  styles:
    - brands
  unicode: f642
allergies:
  changes:
    - 5.0.7
  label: Allergies
  search:
    terms:
      - freckles
      - hand
      - intolerances
      - pox
      - spots
  styles:
    - solid
  unicode: f461
amazon:
  changes:
    - '4.4'
    - 5.0.0
  label: Amazon
  search:
    terms: []
  styles:
    - brands
  unicode: f270
amazon-pay:
  changes:
    - 5.0.2
  label: Amazon Pay
  search:
    terms: []
  styles:
    - brands
  unicode: f42c
ambulance:
  changes:
    - '3'
    - 5.0.0
    - 5.0.7
  label: ambulance
  search:
    terms:
      - help
      - machine
      - support
      - vehicle
  styles:
    - solid
  unicode: f0f9
american-sign-language-interpreting:
  changes:
    - '4.6'
    - 5.0.0
  label: American Sign Language Interpreting
  search:
    terms: []
  styles:
    - solid
  unicode: f2a3
amilia:
  changes:
    - 5.0.0
  label: Amilia
  search:
    terms: []
  styles:
    - brands
  unicode: f36d
anchor:
  changes:
    - '3.1'
    - 5.0.0
  label: Anchor
  search:
    terms:
      - link
  styles:
    - solid
  unicode: f13d
android:
  changes:
    - '3.2'
    - 5.0.0
  label: Android
  search:
    terms:
      - robot
  styles:
    - brands
  unicode: f17b
angellist:
  changes:
    - '4.2'
    - 5.0.0
  label: AngelList
  search:
    terms: []
  styles:
    - brands
  unicode: f209
angle-double-down:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Down
  search:
    terms:
      - arrows
  styles:
    - solid
  unicode: f103
angle-double-left:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Left
  search:
    terms:
      - arrows
      - back
      - laquo
      - previous
      - quote
  styles:
    - solid
  unicode: f100
angle-double-right:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Right
  search:
    terms:
      - arrows
      - forward
      - next
      - quote
      - raquo
  styles:
    - solid
  unicode: f101
angle-double-up:
  changes:
    - '3'
    - 5.0.0
  label: Angle Double Up
  search:
    terms:
      - arrows
  styles:
    - solid
  unicode: f102
angle-down:
  changes:
    - '3'
    - 5.0.0
  label: angle-down
  search:
    terms:
      - arrow
  styles:
    - solid
  unicode: f107
angle-left:
  changes:
    - '3'
    - 5.0.0
  label: angle-left
  search:
    terms:
      - arrow
      - back
      - previous
  styles:
    - solid
  unicode: f104
angle-right:
  changes:
    - '3'
    - 5.0.0
  label: angle-right
  search:
    terms:
      - arrow
      - forward
      - next
  styles:
    - solid
  unicode: f105
angle-up:
  changes:
    - '3'
    - 5.0.0
  label: angle-up
  search:
    terms:
      - arrow
  styles:
    - solid
  unicode: f106
angry:
  changes:
    - 5.1.0
  label: Angry Face
  search:
    terms:
      - disapprove
      - emoticon
      - face
      - mad
      - upset
  styles:
    - solid
    - regular
  unicode: f556
angrycreative:
  changes:
    - 5.0.0
  label: Angry Creative
  search:
    terms: []
  styles:
    - brands
  unicode: f36e
angular:
  changes:
    - 5.0.0
  label: Angular
  search:
    terms: []
  styles:
    - brands
  unicode: f420
ankh:
  changes:
    - 5.3.0
  label: Ankh
  search:
    terms:
      - amulet
      - copper
      - coptic christianity
      - copts
      - crux ansata
      - egyptian
      - venus
  styles:
    - solid
  unicode: f644
app-store:
  changes:
    - 5.0.0
  label: App Store
  search:
    terms: []
  styles:
    - brands
  unicode: f36f
app-store-ios:
  changes:
    - 5.0.0
  label: iOS App Store
  search:
    terms: []
  styles:
    - brands
  unicode: f370
apper:
  changes:
    - 5.0.0
  label: Apper Systems AB
  search:
    terms: []
  styles:
    - brands
  unicode: f371
apple:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.7
  label: Apple
  search:
    terms:
      - food
      - fruit
      - mac
      - osx
  styles:
    - brands
  unicode: f179
apple-alt:
  changes:
    - 5.2.0
  label: Fruit Apple
  search:
    terms:
      - fall
      - food
      - fruit
      - fuji
      - macintosh
      - seasonal
  styles:
    - solid
  unicode: f5d1
apple-pay:
  changes:
    - 5.0.0
  label: Apple Pay
  search:
    terms: []
  styles:
    - brands
  unicode: f415
archive:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.9
  label: Archive
  search:
    terms:
      - box
      - package
      - storage
  styles:
    - solid
  unicode: f187
archway:
  changes:
    - 5.1.0
  label: Archway
  search:
    terms:
      - arc
      - monument
      - road
      - street
  styles:
    - solid
  unicode: f557
arrow-alt-circle-down:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Down
  search:
    terms:
      - arrow-circle-o-down
      - download
  styles:
    - solid
    - regular
  unicode: f358
arrow-alt-circle-left:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Left
  search:
    terms:
      - arrow-circle-o-left
      - back
      - previous
  styles:
    - solid
    - regular
  unicode: f359
arrow-alt-circle-right:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Right
  search:
    terms:
      - arrow-circle-o-right
      - forward
      - next
  styles:
    - solid
    - regular
  unicode: f35a
arrow-alt-circle-up:
  changes:
    - 5.0.0
  label: Alternate Arrow Circle Up
  search:
    terms:
      - arrow-circle-o-up
  styles:
    - solid
    - regular
  unicode: f35b
arrow-circle-down:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Down
  search:
    terms:
      - download
  styles:
    - solid
  unicode: f0ab
arrow-circle-left:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Left
  search:
    terms:
      - back
      - previous
  styles:
    - solid
  unicode: f0a8
arrow-circle-right:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Right
  search:
    terms:
      - forward
      - next
  styles:
    - solid
  unicode: f0a9
arrow-circle-up:
  changes:
    - '2'
    - 5.0.0
  label: Arrow Circle Up
  search:
    terms: []
  styles:
    - solid
  unicode: f0aa
arrow-down:
  changes:
    - '1'
    - 5.0.0
  label: arrow-down
  search:
    terms:
      - download
  styles:
    - solid
  unicode: f063
arrow-left:
  changes:
    - '1'
    - 5.0.0
  label: arrow-left
  search:
    terms:
      - back
      - previous
  styles:
    - solid
  unicode: f060
arrow-right:
  changes:
    - '1'
    - 5.0.0
  label: arrow-right
  search:
    terms:
      - forward
      - next
  styles:
    - solid
  unicode: f061
arrow-up:
  changes:
    - '1'
    - 5.0.0
  label: arrow-up
  search:
    terms: []
  styles:
    - solid
  unicode: f062
arrows-alt:
  changes:
    - '2'
    - 5.0.0
  label: Alternate Arrows
  search:
    terms:
      - arrow
      - arrows
      - bigger
      - enlarge
      - expand
      - fullscreen
      - move
      - position
      - reorder
      - resize
  styles:
    - solid
  unicode: f0b2
arrows-alt-h:
  changes:
    - 5.0.0
  label: Alternate Arrows Horizontal
  search:
    terms:
      - arrows-h
      - resize
  styles:
    - solid
  unicode: f337
arrows-alt-v:
  changes:
    - 5.0.0
  label: Alternate Arrows Vertical
  search:
    terms:
      - arrows-v
      - resize
  styles:
    - solid
  unicode: f338
assistive-listening-systems:
  changes:
    - '4.6'
    - 5.0.0
  label: Assistive Listening Systems
  search:
    terms: []
  styles:
    - solid
  unicode: f2a2
asterisk:
  changes:
    - '1'
    - 5.0.0
  label: asterisk
  search:
    terms:
      - details
  styles:
    - solid
  unicode: f069
asymmetrik:
  changes:
    - 5.0.0
  label: 'Asymmetrik, Ltd.'
  search:
    terms: []
  styles:
    - brands
  unicode: f372
at:
  changes:
    - '4.2'
    - 5.0.0
  label: At
  search:
    terms:
      - e-mail
      - email
  styles:
    - solid
  unicode: f1fa
atlas:
  changes:
    - 5.1.0
  label: Atlas
  search:
    terms:
      - book
      - directions
      - geography
      - map
      - wayfinding
  styles:
    - solid
  unicode: f558
atom:
  changes:
    - 5.2.0
  label: Atom
  search:
    terms:
      - atheism
      - chemistry
      - science
  styles:
    - solid
  unicode: f5d2
audible:
  changes:
    - 5.0.0
  label: Audible
  search:
    terms: []
  styles:
    - brands
  unicode: f373
audio-description:
  changes:
    - '4.6'
    - 5.0.0
  label: Audio Description
  search:
    terms: []
  styles:
    - solid
  unicode: f29e
autoprefixer:
  changes:
    - 5.0.0
  label: Autoprefixer
  search:
    terms: []
  styles:
    - brands
  unicode: f41c
avianex:
  changes:
    - 5.0.0
  label: avianex
  search:
    terms: []
  styles:
    - brands
  unicode: f374
aviato:
  changes:
    - 5.0.0
  label: Aviato
  search:
    terms: []
  styles:
    - brands
  unicode: f421
award:
  changes:
    - 5.1.0
    - 5.2.0
  label: Award
  search:
    terms:
      - honor
      - praise
      - prize
      - recognition
      - ribbon
  styles:
    - solid
  unicode: f559
aws:
  changes:
    - 5.0.0
    - 5.1.0
  label: Amazon Web Services (AWS)
  search:
    terms: []
  styles:
    - brands
  unicode: f375
backspace:
  changes:
    - 5.1.0
  label: Backspace
  search:
    terms:
      - command
      - delete
      - keyboard
      - undo
  styles:
    - solid
  unicode: f55a
backward:
  changes:
    - '1'
    - 5.0.0
  label: backward
  search:
    terms:
      - previous
      - rewind
  styles:
    - solid
  unicode: f04a
balance-scale:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.13
  label: Balance Scale
  search:
    terms:
      - balanced
      - justice
      - legal
      - measure
      - weight
  styles:
    - solid
  unicode: f24e
ban:
  changes:
    - '1'
    - 5.0.0
  label: ban
  search:
    terms:
      - abort
      - ban
      - block
      - cancel
      - delete
      - hide
      - prohibit
      - remove
      - stop
      - trash
  styles:
    - solid
  unicode: f05e
band-aid:
  changes:
    - 5.0.7
  label: Band-Aid
  search:
    terms:
      - bandage
      - boo boo
      - ouch
  styles:
    - solid
  unicode: f462
bandcamp:
  changes:
    - '4.7'
    - 5.0.0
  label: Bandcamp
  search:
    terms: []
  styles:
    - brands
  unicode: f2d5
barcode:
  changes:
    - '1'
    - 5.0.0
  label: barcode
  search:
    terms:
      - scan
  styles:
    - solid
  unicode: f02a
bars:
  changes:
    - '2'
    - 5.0.0
  label: Bars
  search:
    terms:
      - checklist
      - drag
      - hamburger
      - list
      - menu
      - nav
      - navigation
      - ol
      - reorder
      - settings
      - todo
      - ul
  styles:
    - solid
  unicode: f0c9
baseball-ball:
  changes:
    - 5.0.5
  label: Baseball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f433
basketball-ball:
  changes:
    - 5.0.5
  label: Basketball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f434
bath:
  changes:
    - '4.7'
    - 5.0.0
  label: Bath
  search:
    terms: []
  styles:
    - solid
  unicode: f2cd
battery-empty:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery Empty
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f244
battery-full:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f240
battery-half:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery 1/2 Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f242
battery-quarter:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery 1/4 Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f243
battery-three-quarters:
  changes:
    - '4.4'
    - 5.0.0
  label: Battery 3/4 Full
  search:
    terms:
      - power
      - status
  styles:
    - solid
  unicode: f241
bed:
  changes:
    - '4.3'
    - 5.0.0
    - 5.1.0
  label: Bed
  search:
    terms:
      - lodging
      - sleep
      - travel
  styles:
    - solid
  unicode: f236
beer:
  changes:
    - '3'
    - 5.0.0
  label: beer
  search:
    terms:
      - alcohol
      - bar
      - beverage
      - drink
      - liquor
      - mug
      - stein
  styles:
    - solid
  unicode: f0fc
behance:
  changes:
    - '4.1'
    - 5.0.0
  label: Behance
  search:
    terms: []
  styles:
    - brands
  unicode: f1b4
behance-square:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: Behance Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1b5
bell:
  changes:
    - '2'
    - 5.0.0
    - 5.2.0
  label: bell
  search:
    terms:
      - alert
      - notification
      - reminder
  styles:
    - solid
    - regular
  unicode: f0f3
bell-slash:
  changes:
    - '4.2'
    - 5.0.0
    - 5.2.0
  label: Bell Slash
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1f6
bezier-curve:
  changes:
    - 5.1.0
  label: Bezier Curve
  search:
    terms:
      - curves
      - illustrator
      - lines
      - path
      - vector
  styles:
    - solid
  unicode: f55b
bible:
  changes:
    - 5.3.0
  label: Bible
  search:
    terms:
      - book
      - catholicism
      - christianity
  styles:
    - solid
  unicode: f647
bicycle:
  changes:
    - '4.2'
    - 5.0.0
  label: Bicycle
  search:
    terms:
      - bike
      - gears
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f206
bimobject:
  changes:
    - 5.0.0
  label: BIMobject
  search:
    terms: []
  styles:
    - brands
  unicode: f378
binoculars:
  changes:
    - '4.2'
    - 5.0.0
    - 5.2.0
  label: Binoculars
  search:
    terms: []
  styles:
    - solid
  unicode: f1e5
birthday-cake:
  changes:
    - '4.2'
    - 5.0.0
  label: Birthday Cake
  search:
    terms: []
  styles:
    - solid
  unicode: f1fd
bitbucket:
  changes:
    - '3.2'
    - 5.0.0
  label: Bitbucket
  search:
    terms:
      - bitbucket-square
      - git
  styles:
    - brands
  unicode: f171
bitcoin:
  changes:
    - 5.0.0
  label: Bitcoin
  search:
    terms: []
  styles:
    - brands
  unicode: f379
bity:
  changes:
    - 5.0.0
  label: Bity
  search:
    terms: []
  styles:
    - brands
  unicode: f37a
black-tie:
  changes:
    - '4.4'
    - 5.0.0
  label: Font Awesome Black Tie
  search:
    terms: []
  styles:
    - brands
  unicode: f27e
blackberry:
  changes:
    - 5.0.0
  label: BlackBerry
  search:
    terms: []
  styles:
    - brands
  unicode: f37b
blender:
  changes:
    - 5.0.13
  label: Blender
  search:
    terms: []
  styles:
    - solid
  unicode: f517
blender-phone:
  changes:
    - 5.4.0
  label: Blender Phone
  search:
    terms:
      - appliance
      - fantasy
      - silly
  styles:
    - solid
  unicode: f6b6
blind:
  changes:
    - '4.6'
    - 5.0.0
  label: Blind
  search:
    terms: []
  styles:
    - solid
  unicode: f29d
blogger:
  changes:
    - 5.0.0
  label: Blogger
  search:
    terms: []
  styles:
    - brands
  unicode: f37c
blogger-b:
  changes:
    - 5.0.0
  label: Blogger B
  search:
    terms: []
  styles:
    - brands
  unicode: f37d
bluetooth:
  changes:
    - '4.5'
    - 5.0.0
  label: Bluetooth
  search:
    terms: []
  styles:
    - brands
  unicode: f293
bluetooth-b:
  changes:
    - '4.5'
    - 5.0.0
  label: Bluetooth
  search:
    terms: []
  styles:
    - brands
  unicode: f294
bold:
  changes:
    - '1'
    - 5.0.0
  label: bold
  search:
    terms: []
  styles:
    - solid
  unicode: f032
bolt:
  changes:
    - '2'
    - 5.0.0
    - 5.5.0
  label: Lightning Bolt
  search:
    terms:
      - electricity
      - lightning
      - weather
      - zap
  styles:
    - solid
  unicode: f0e7
bomb:
  changes:
    - '4.1'
    - 5.0.0
  label: Bomb
  search:
    terms: []
  styles:
    - solid
  unicode: f1e2
bone:
  changes:
    - 5.2.0
  label: Bone
  search:
    terms: []
  styles:
    - solid
  unicode: f5d7
bong:
  changes:
    - 5.1.0
  label: Bong
  search:
    terms:
      - aparatus
      - cannabis
      - marijuana
      - pipe
      - smoke
      - smoking
  styles:
    - solid
  unicode: f55c
book:
  changes:
    - '1'
    - 5.0.0
  label: book
  search:
    terms:
      - documentation
      - read
  styles:
    - solid
  unicode: f02d
book-dead:
  changes:
    - 5.4.0
  label: Book of the Dead
  search:
    terms:
      - Dungeons & Dragons
      - crossbones
      - d&d
      - dark arts
      - death
      - dnd
      - documentation
      - evil
      - fantasy
      - halloween
      - holiday
      - read
      - skull
      - spell
  styles:
    - solid
  unicode: f6b7
book-open:
  changes:
    - 5.0.13
    - 5.1.0
    - 5.2.0
  label: Book Open
  search:
    terms:
      - flyer
      - notebook
      - open book
      - pamphlet
      - reading
  styles:
    - solid
  unicode: f518
book-reader:
  changes:
    - 5.2.0
  label: Book Reader
  search:
    terms:
      - library
  styles:
    - solid
  unicode: f5da
bookmark:
  changes:
    - '1'
    - 5.0.0
  label: bookmark
  search:
    terms:
      - save
  styles:
    - solid
    - regular
  unicode: f02e
bowling-ball:
  changes:
    - 5.0.5
  label: Bowling Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f436
box:
  changes:
    - 5.0.7
  label: Box
  search:
    terms:
      - package
  styles:
    - solid
  unicode: f466
box-open:
  changes:
    - 5.0.9
  label: Box Open
  search:
    terms: []
  styles:
    - solid
  unicode: f49e
boxes:
  changes:
    - 5.0.7
  label: Boxes
  search:
    terms: []
  styles:
    - solid
  unicode: f468
braille:
  changes:
    - '4.6'
    - 5.0.0
  label: Braille
  search:
    terms: []
  styles:
    - solid
  unicode: f2a1
brain:
  changes:
    - 5.2.0
  label: Brain
  search:
    terms:
      - cerebellum
      - gray matter
      - intellect
      - medulla oblongata
      - mind
      - noodle
      - wit
  styles:
    - solid
  unicode: f5dc
briefcase:
  changes:
    - '2'
    - 5.0.0
    - 5.3.0
  label: Briefcase
  search:
    terms:
      - bag
      - business
      - luggage
      - office
      - work
  styles:
    - solid
  unicode: f0b1
briefcase-medical:
  changes:
    - 5.0.7
  label: Medical Briefcase
  search:
    terms:
      - health briefcase
  styles:
    - solid
  unicode: f469
broadcast-tower:
  changes:
    - 5.0.13
  label: Broadcast Tower
  search:
    terms:
      - airwaves
      - radio
      - waves
  styles:
    - solid
  unicode: f519
broom:
  changes:
    - 5.0.13
  label: Broom
  search:
    terms:
      - clean
      - firebolt
      - fly
      - halloween
      - holiday
      - nimbus 2000
      - quidditch
      - sweep
      - witch
  styles:
    - solid
  unicode: f51a
brush:
  changes:
    - 5.1.0
  label: Brush
  search:
    terms:
      - bristles
      - color
      - handle
      - painting
  styles:
    - solid
  unicode: f55d
btc:
  changes:
    - '3.2'
    - 5.0.0
  label: BTC
  search:
    terms: []
  styles:
    - brands
  unicode: f15a
bug:
  changes:
    - '3.2'
    - 5.0.0
  label: Bug
  search:
    terms:
      - insect
      - report
  styles:
    - solid
  unicode: f188
building:
  changes:
    - '4.1'
    - 5.0.0
  label: Building
  search:
    terms:
      - apartment
      - business
      - company
      - office
      - work
  styles:
    - solid
    - regular
  unicode: f1ad
bullhorn:
  changes:
    - '2'
    - 5.0.0
    - 5.3.0
  label: bullhorn
  search:
    terms:
      - announcement
      - broadcast
      - louder
      - megaphone
      - share
  styles:
    - solid
  unicode: f0a1
bullseye:
  changes:
    - '3.1'
    - 5.0.0
    - 5.3.0
  label: Bullseye
  search:
    terms:
      - target
  styles:
    - solid
  unicode: f140
burn:
  changes:
    - 5.0.7
  label: Burn
  search:
    terms:
      - energy
  styles:
    - solid
  unicode: f46a
buromobelexperte:
  changes:
    - 5.0.0
  label: Büromöbel-Experte GmbH & Co. KG.
  search:
    terms: []
  styles:
    - brands
  unicode: f37f
bus:
  changes:
    - '4.2'
    - 5.0.0
    - 5.1.0
  label: Bus
  search:
    terms:
      - machine
      - public transportation
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f207
bus-alt:
  changes:
    - 5.1.0
  label: Bus Alt
  search:
    terms:
      - machine
      - public transportation
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f55e
business-time:
  changes:
    - 5.3.0
  label: Business Time
  search:
    terms:
      - briefcase
      - business socks
      - clock
      - flight of the conchords
      - wednesday
  styles:
    - solid
  unicode: f64a
buysellads:
  changes:
    - '4.3'
    - 5.0.0
  label: BuySellAds
  search:
    terms: []
  styles:
    - brands
  unicode: f20d
calculator:
  changes:
    - '4.2'
    - 5.0.0
    - 5.3.0
  label: Calculator
  search:
    terms: []
  styles:
    - solid
  unicode: f1ec
calendar:
  changes:
    - '3.1'
    - 5.0.0
  label: Calendar
  search:
    terms:
      - calendar-o
      - date
      - event
      - schedule
      - time
      - when
  styles:
    - solid
    - regular
  unicode: f133
calendar-alt:
  changes:
    - '1'
    - 5.0.0
  label: Alternate Calendar
  search:
    terms:
      - calendar
      - date
      - event
      - schedule
      - time
      - when
  styles:
    - solid
    - regular
  unicode: f073
calendar-check:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Check
  search:
    terms:
      - accept
      - agree
      - appointment
      - confirm
      - correct
      - done
      - ok
      - select
      - success
      - todo
  styles:
    - solid
    - regular
  unicode: f274
calendar-minus:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Minus
  search:
    terms:
      - delete
      - negative
      - remove
  styles:
    - solid
    - regular
  unicode: f272
calendar-plus:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Plus
  search:
    terms:
      - add
      - create
      - new
      - positive
  styles:
    - solid
    - regular
  unicode: f271
calendar-times:
  changes:
    - '4.4'
    - 5.0.0
  label: Calendar Times
  search:
    terms:
      - archive
      - delete
      - remove
      - x
  styles:
    - solid
    - regular
  unicode: f273
camera:
  changes:
    - '1'
    - 5.0.0
  label: camera
  search:
    terms:
      - photo
      - picture
      - record
  styles:
    - solid
  unicode: f030
camera-retro:
  changes:
    - '1'
    - 5.0.0
  label: Retro Camera
  search:
    terms:
      - photo
      - picture
      - record
  styles:
    - solid
  unicode: f083
campground:
  changes:
    - 5.4.0
  label: Campground
  search:
    terms:
      - camping
      - fall
      - outdoors
      - seasonal
      - tent
  styles:
    - solid
  unicode: f6bb
cannabis:
  changes:
    - 5.1.0
  label: Cannabis
  search:
    terms:
      - bud
      - chronic
      - drugs
      - endica
      - endo
      - ganja
      - marijuana
      - mary jane
      - pot
      - reefer
      - sativa
      - spliff
      - weed
      - whacky-tabacky
  styles:
    - solid
  unicode: f55f
capsules:
  changes:
    - 5.0.7
  label: Capsules
  search:
    terms:
      - drugs
      - medicine
  styles:
    - solid
  unicode: f46b
car:
  changes:
    - '4.1'
    - 5.0.0
    - 5.2.0
  label: Car
  search:
    terms:
      - machine
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f1b9
car-alt:
  changes:
    - 5.2.0
  label: Alternate Car
  search:
    terms: []
  styles:
    - solid
  unicode: f5de
car-battery:
  changes:
    - 5.2.0
  label: Car Battery
  search:
    terms: []
  styles:
    - solid
  unicode: f5df
car-crash:
  changes:
    - 5.2.0
  label: Car Crash
  search:
    terms: []
  styles:
    - solid
  unicode: f5e1
car-side:
  changes:
    - 5.2.0
  label: Car Side
  search:
    terms: []
  styles:
    - solid
  unicode: f5e4
caret-down:
  changes:
    - '2'
    - 5.0.0
  label: Caret Down
  search:
    terms:
      - arrow
      - dropdown
      - menu
      - more
      - triangle down
  styles:
    - solid
  unicode: f0d7
caret-left:
  changes:
    - '2'
    - 5.0.0
  label: Caret Left
  search:
    terms:
      - arrow
      - back
      - previous
      - triangle left
  styles:
    - solid
  unicode: f0d9
caret-right:
  changes:
    - '2'
    - 5.0.0
  label: Caret Right
  search:
    terms:
      - arrow
      - forward
      - next
      - triangle right
  styles:
    - solid
  unicode: f0da
caret-square-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Caret Square Down
  search:
    terms:
      - caret-square-o-down
      - dropdown
      - menu
      - more
  styles:
    - solid
    - regular
  unicode: f150
caret-square-left:
  changes:
    - '4'
    - 5.0.0
  label: Caret Square Left
  search:
    terms:
      - back
      - caret-square-o-left
      - previous
  styles:
    - solid
    - regular
  unicode: f191
caret-square-right:
  changes:
    - '3.2'
    - 5.0.0
  label: Caret Square Right
  search:
    terms:
      - caret-square-o-right
      - forward
      - next
  styles:
    - solid
    - regular
  unicode: f152
caret-square-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Caret Square Up
  search:
    terms:
      - caret-square-o-up
  styles:
    - solid
    - regular
  unicode: f151
caret-up:
  changes:
    - '2'
    - 5.0.0
  label: Caret Up
  search:
    terms:
      - arrow
      - triangle up
  styles:
    - solid
  unicode: f0d8
cart-arrow-down:
  changes:
    - '4.3'
    - 5.0.0
  label: Shopping Cart Arrow Down
  search:
    terms:
      - shopping
  styles:
    - solid
  unicode: f218
cart-plus:
  changes:
    - '4.3'
    - 5.0.0
  label: Add to Shopping Cart
  search:
    terms:
      - add
      - create
      - new
      - positive
      - shopping
  styles:
    - solid
  unicode: f217
cat:
  changes:
    - 5.4.0
  label: Cat
  search:
    terms:
      - feline
      - halloween
      - holiday
      - kitten
      - kitty
      - meow
      - pet
  styles:
    - solid
  unicode: f6be
cc-amazon-pay:
  changes:
    - 5.0.2
  label: Amazon Pay Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f42d
cc-amex:
  changes:
    - '4.2'
    - 5.0.0
  label: American Express Credit Card
  search:
    terms:
      - amex
  styles:
    - brands
  unicode: f1f3
cc-apple-pay:
  changes:
    - 5.0.0
  label: Apple Pay Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f416
cc-diners-club:
  changes:
    - '4.4'
    - 5.0.0
  label: Diner's Club Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f24c
cc-discover:
  changes:
    - '4.2'
    - 5.0.0
  label: Discover Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f2
cc-jcb:
  changes:
    - '4.4'
    - 5.0.0
  label: JCB Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f24b
cc-mastercard:
  changes:
    - '4.2'
    - 5.0.0
  label: MasterCard Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f1
cc-paypal:
  changes:
    - '4.2'
    - 5.0.0
  label: Paypal Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f4
cc-stripe:
  changes:
    - '4.2'
    - 5.0.0
  label: Stripe Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f5
cc-visa:
  changes:
    - '4.2'
    - 5.0.0
  label: Visa Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f0
centercode:
  changes:
    - 5.0.0
  label: Centercode
  search:
    terms: []
  styles:
    - brands
  unicode: f380
certificate:
  changes:
    - '2'
    - 5.0.0
  label: certificate
  search:
    terms:
      - badge
      - star
  styles:
    - solid
  unicode: f0a3
chair:
  changes:
    - 5.4.0
  label: Chair
  search:
    terms:
      - furniture
      - seat
  styles:
    - solid
  unicode: f6c0
chalkboard:
  changes:
    - 5.0.13
  label: Chalkboard
  search:
    terms:
      - blackboard
      - learning
      - school
      - teaching
      - whiteboard
      - writing
  styles:
    - solid
  unicode: f51b
chalkboard-teacher:
  changes:
    - 5.0.13
  label: Chalkboard Teacher
  search:
    terms:
      - blackboard
      - instructor
      - learning
      - professor
      - school
      - whiteboard
      - writing
  styles:
    - solid
  unicode: f51c
charging-station:
  changes:
    - 5.2.0
  label: Charging Station
  search:
    terms: []
  styles:
    - solid
  unicode: f5e7
chart-area:
  changes:
    - '4.2'
    - 5.0.0
  label: Area Chart
  search:
    terms:
      - analytics
      - area-chart
      - graph
  styles:
    - solid
  unicode: f1fe
chart-bar:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: Bar Chart
  search:
    terms:
      - analytics
      - bar-chart
      - graph
  styles:
    - solid
    - regular
  unicode: f080
chart-line:
  changes:
    - '4.2'
    - 5.0.0
    - 5.3.0
  label: Line Chart
  search:
    terms:
      - activity
      - analytics
      - dashboard
      - graph
      - line-chart
  styles:
    - solid
  unicode: f201
chart-pie:
  changes:
    - '4.2'
    - 5.0.0
    - 5.3.0
  label: Pie Chart
  search:
    terms:
      - analytics
      - graph
      - pie-chart
  styles:
    - solid
  unicode: f200
check:
  changes:
    - '1'
    - 5.0.0
  label: Check
  search:
    terms:
      - accept
      - agree
      - checkmark
      - confirm
      - correct
      - done
      - notice
      - notification
      - notify
      - ok
      - select
      - success
      - tick
      - todo
      - 'yes'
  styles:
    - solid
  unicode: f00c
check-circle:
  changes:
    - '1'
    - 5.0.0
  label: Check Circle
  search:
    terms:
      - accept
      - agree
      - confirm
      - correct
      - done
      - ok
      - select
      - success
      - todo
      - 'yes'
  styles:
    - solid
    - regular
  unicode: f058
check-double:
  changes:
    - 5.1.0
  label: Check Double
  search:
    terms:
      - accept
      - agree
      - checkmark
      - confirm
      - correct
      - done
      - notice
      - notification
      - notify
      - ok
      - select
      - success
      - tick
      - todo
  styles:
    - solid
  unicode: f560
check-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Check Square
  search:
    terms:
      - accept
      - agree
      - checkmark
      - confirm
      - correct
      - done
      - ok
      - select
      - success
      - todo
      - 'yes'
  styles:
    - solid
    - regular
  unicode: f14a
chess:
  changes:
    - 5.0.5
  label: Chess
  search:
    terms: []
  styles:
    - solid
  unicode: f439
chess-bishop:
  changes:
    - 5.0.5
  label: Chess Bishop
  search:
    terms: []
  styles:
    - solid
  unicode: f43a
chess-board:
  changes:
    - 5.0.5
  label: Chess Board
  search:
    terms: []
  styles:
    - solid
  unicode: f43c
chess-king:
  changes:
    - 5.0.5
  label: Chess King
  search:
    terms: []
  styles:
    - solid
  unicode: f43f
chess-knight:
  changes:
    - 5.0.5
  label: Chess Knight
  search:
    terms: []
  styles:
    - solid
  unicode: f441
chess-pawn:
  changes:
    - 5.0.5
  label: Chess Pawn
  search:
    terms: []
  styles:
    - solid
  unicode: f443
chess-queen:
  changes:
    - 5.0.5
  label: Chess Queen
  search:
    terms: []
  styles:
    - solid
  unicode: f445
chess-rook:
  changes:
    - 5.0.5
  label: Chess Rook
  search:
    terms: []
  styles:
    - solid
  unicode: f447
chevron-circle-down:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Down
  search:
    terms:
      - arrow
      - dropdown
      - menu
      - more
  styles:
    - solid
  unicode: f13a
chevron-circle-left:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Left
  search:
    terms:
      - arrow
      - back
      - previous
  styles:
    - solid
  unicode: f137
chevron-circle-right:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Right
  search:
    terms:
      - arrow
      - forward
      - next
  styles:
    - solid
  unicode: f138
chevron-circle-up:
  changes:
    - '3.1'
    - 5.0.0
  label: Chevron Circle Up
  search:
    terms:
      - arrow
  styles:
    - solid
  unicode: f139
chevron-down:
  changes:
    - '1'
    - 5.0.0
  label: chevron-down
  search:
    terms: []
  styles:
    - solid
  unicode: f078
chevron-left:
  changes:
    - '1'
    - 5.0.0
  label: chevron-left
  search:
    terms:
      - back
      - bracket
      - previous
  styles:
    - solid
  unicode: f053
chevron-right:
  changes:
    - '1'
    - 5.0.0
  label: chevron-right
  search:
    terms:
      - bracket
      - forward
      - next
  styles:
    - solid
  unicode: f054
chevron-up:
  changes:
    - '1'
    - 5.0.0
  label: chevron-up
  search:
    terms: []
  styles:
    - solid
  unicode: f077
child:
  changes:
    - '4.1'
    - 5.0.0
  label: Child
  search:
    terms: []
  styles:
    - solid
  unicode: f1ae
chrome:
  changes:
    - '4.4'
    - 5.0.0
  label: Chrome
  search:
    terms:
      - browser
  styles:
    - brands
  unicode: f268
church:
  changes:
    - 5.0.13
  label: Church
  search:
    terms:
      - building
      - community
      - religion
  styles:
    - solid
  unicode: f51d
circle:
  changes:
    - '3'
    - 5.0.0
  label: Circle
  search:
    terms:
      - circle-thin
      - dot
      - notification
  styles:
    - solid
    - regular
  unicode: f111
circle-notch:
  changes:
    - '4.1'
    - 5.0.0
  label: Circle Notched
  search:
    terms:
      - circle-o-notch
  styles:
    - solid
  unicode: f1ce
city:
  changes:
    - 5.3.0
  label: City
  search:
    terms:
      - buildings
      - busy
      - skyscrapers
      - urban
      - windows
  styles:
    - solid
  unicode: f64f
clipboard:
  changes:
    - 5.0.0
  label: Clipboard
  search:
    terms:
      - paste
  styles:
    - solid
    - regular
  unicode: f328
clipboard-check:
  changes:
    - 5.0.7
  label: Clipboard with Check
  search:
    terms:
      - accept
      - agree
      - confirm
      - done
      - ok
      - select
      - success
      - todo
      - 'yes'
  styles:
    - solid
  unicode: f46c
clipboard-list:
  changes:
    - 5.0.7
  label: Clipboard List
  search:
    terms:
      - checklist
      - completed
      - done
      - finished
      - intinerary
      - ol
      - schedule
      - todo
      - ul
  styles:
    - solid
  unicode: f46d
clock:
  changes:
    - '1'
    - 5.0.0
  label: Clock
  search:
    terms:
      - date
      - late
      - schedule
      - timer
      - timestamp
      - watch
  styles:
    - solid
    - regular
  unicode: f017
clone:
  changes:
    - '4.4'
    - 5.0.0
  label: Clone
  search:
    terms:
      - copy
      - duplicate
  styles:
    - solid
    - regular
  unicode: f24d
closed-captioning:
  changes:
    - '4.2'
    - 5.0.0
  label: Closed Captioning
  search:
    terms:
      - cc
  styles:
    - solid
    - regular
  unicode: f20a
cloud:
  changes:
    - '2'
    - 5.0.0
    - 5.0.11
  label: Cloud
  search:
    terms:
      - save
  styles:
    - solid
  unicode: f0c2
cloud-download-alt:
  changes:
    - 5.0.0
    - 5.0.11
  label: Alternate Cloud Download
  search:
    terms:
      - import
  styles:
    - solid
  unicode: f381
cloud-meatball:
  changes:
    - 5.5.0
  label: Cloud with (a chance of) Meatball
  search:
    terms: []
  styles:
    - solid
  unicode: f73b
cloud-moon:
  changes:
    - 5.4.0
    - 5.5.0
  label: Cloud with Moon
  search:
    terms:
      - crescent
      - evening
      - halloween
      - holiday
      - lunar
      - night
      - sky
  styles:
    - solid
  unicode: f6c3
cloud-moon-rain:
  changes:
    - 5.5.0
  label: Cloud with Moon and Rain
  search:
    terms: []
  styles:
    - solid
  unicode: f73c
cloud-rain:
  changes:
    - 5.5.0
  label: Cloud with Rain
  search:
    terms:
      - precipitation
  styles:
    - solid
  unicode: f73d
cloud-showers-heavy:
  changes:
    - 5.5.0
  label: Cloud with Heavy Showers
  search:
    terms:
      - precipitation
      - rain
      - storm
  styles:
    - solid
  unicode: f740
cloud-sun:
  changes:
    - 5.4.0
    - 5.5.0
  label: Cloud with Sun
  search:
    terms:
      - day
      - daytime
      - fall
      - outdoors
      - seasonal
  styles:
    - solid
  unicode: f6c4
cloud-sun-rain:
  changes:
    - 5.5.0
  label: Cloud with Sun and Rain
  search:
    terms: []
  styles:
    - solid
  unicode: f743
cloud-upload-alt:
  changes:
    - 5.0.0
    - 5.0.11
  label: Alternate Cloud Upload
  search:
    terms:
      - cloud-upload
  styles:
    - solid
  unicode: f382
cloudscale:
  changes:
    - 5.0.0
  label: cloudscale.ch
  search:
    terms: []
  styles:
    - brands
  unicode: f383
cloudsmith:
  changes:
    - 5.0.0
  label: Cloudsmith
  search:
    terms: []
  styles:
    - brands
  unicode: f384
cloudversify:
  changes:
    - 5.0.0
  label: cloudversify
  search:
    terms: []
  styles:
    - brands
  unicode: f385
cocktail:
  changes:
    - 5.1.0
  label: Cocktail
  search:
    terms:
      - alcohol
      - beverage
      - drink
  styles:
    - solid
  unicode: f561
code:
  changes:
    - '3.1'
    - 5.0.0
  label: Code
  search:
    terms:
      - brackets
      - html
  styles:
    - solid
  unicode: f121
code-branch:
  changes:
    - 5.0.0
  label: Code Branch
  search:
    terms:
      - branch
      - code-fork
      - fork
      - git
      - github
      - rebase
      - svn
      - vcs
      - version
  styles:
    - solid
  unicode: f126
codepen:
  changes:
    - '4.1'
    - 5.0.0
  label: Codepen
  search:
    terms: []
  styles:
    - brands
  unicode: f1cb
codiepie:
  changes:
    - '4.5'
    - 5.0.0
  label: Codie Pie
  search:
    terms: []
  styles:
    - brands
  unicode: f284
coffee:
  changes:
    - '3'
    - 5.0.0
  label: Coffee
  search:
    terms:
      - beverage
      - breakfast
      - cafe
      - drink
      - fall
      - morning
      - mug
      - seasonal
      - tea
  styles:
    - solid
  unicode: f0f4
cog:
  changes:
    - '1'
    - 5.0.0
  label: cog
  search:
    terms:
      - settings
  styles:
    - solid
  unicode: f013
cogs:
  changes:
    - '1'
    - 5.0.0
  label: cogs
  search:
    terms:
      - gears
      - settings
  styles:
    - solid
  unicode: f085
coins:
  changes:
    - 5.0.13
  label: Coins
  search:
    terms: []
  styles:
    - solid
  unicode: f51e
columns:
  changes:
    - '2'
    - 5.0.0
  label: Columns
  search:
    terms:
      - dashboard
      - panes
      - split
  styles:
    - solid
  unicode: f0db
comment:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: comment
  search:
    terms:
      - bubble
      - chat
      - conversation
      - feedback
      - message
      - note
      - notification
      - sms
      - speech
      - texting
  styles:
    - solid
    - regular
  unicode: f075
comment-alt:
  changes:
    - '4.4'
    - 5.0.0
  label: Alternate Comment
  search:
    terms:
      - bubble
      - chat
      - commenting
      - conversation
      - feedback
      - message
      - note
      - notification
      - sms
      - speech
      - texting
  styles:
    - solid
    - regular
  unicode: f27a
comment-dollar:
  changes:
    - 5.3.0
  label: Comment Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f651
comment-dots:
  changes:
    - 5.0.9
  label: Comment Dots
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f4ad
comment-slash:
  changes:
    - 5.0.9
  label: Comment Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4b3
comments:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: comments
  search:
    terms:
      - bubble
      - chat
      - conversation
      - feedback
      - message
      - note
      - notification
      - sms
      - speech
      - texting
  styles:
    - solid
    - regular
  unicode: f086
comments-dollar:
  changes:
    - 5.3.0
  label: Comments Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f653
compact-disc:
  changes:
    - 5.0.13
  label: Compact Disc
  search:
    terms:
      - bluray
      - cd
      - disc
      - media
  styles:
    - solid
  unicode: f51f
compass:
  changes:
    - '3.2'
    - 5.0.0
    - 5.2.0
  label: Compass
  search:
    terms:
      - directory
      - location
      - menu
      - safari
  styles:
    - solid
    - regular
  unicode: f14e
compress:
  changes:
    - 5.0.0
  label: Compress
  search:
    terms:
      - collapse
      - combine
      - contract
      - merge
      - smaller
  styles:
    - solid
  unicode: f066
concierge-bell:
  changes:
    - 5.1.0
  label: Concierge Bell
  search:
    terms:
      - attention
      - hotel
      - service
      - support
  styles:
    - solid
  unicode: f562
connectdevelop:
  changes:
    - '4.3'
    - 5.0.0
  label: Connect Develop
  search:
    terms: []
  styles:
    - brands
  unicode: f20e
contao:
  changes:
    - '4.4'
    - 5.0.0
  label: Contao
  search:
    terms: []
  styles:
    - brands
  unicode: f26d
cookie:
  changes:
    - 5.1.0
  label: Cookie
  search:
    terms:
      - baked good
      - chips
      - food
      - snack
      - sweet
      - treat
  styles:
    - solid
  unicode: f563
cookie-bite:
  changes:
    - 5.1.0
  label: Cookie Bite
  search:
    terms:
      - baked good
      - bitten
      - chips
      - eating
      - food
      - snack
      - sweet
      - treat
  styles:
    - solid
  unicode: f564
copy:
  changes:
    - '2'
    - 5.0.0
  label: Copy
  search:
    terms:
      - clone
      - duplicate
      - file
      - files-o
  styles:
    - solid
    - regular
  unicode: f0c5
copyright:
  changes:
    - '4.2'
    - 5.0.0
  label: Copyright
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1f9
couch:
  changes:
    - 5.0.9
  label: Couch
  search:
    terms:
      - furniture
      - sofa
  styles:
    - solid
  unicode: f4b8
cpanel:
  changes:
    - 5.0.0
  label: cPanel
  search:
    terms: []
  styles:
    - brands
  unicode: f388
creative-commons:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.11
    - 5.1.0
  label: Creative Commons
  search:
    terms: []
  styles:
    - brands
  unicode: f25e
creative-commons-by:
  changes:
    - 5.0.11
  label: Creative Commons Attribution
  search:
    terms: []
  styles:
    - brands
  unicode: f4e7
creative-commons-nc:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial
  search:
    terms: []
  styles:
    - brands
  unicode: f4e8
creative-commons-nc-eu:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial (Euro Sign)
  search:
    terms: []
  styles:
    - brands
  unicode: f4e9
creative-commons-nc-jp:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial (Yen Sign)
  search:
    terms: []
  styles:
    - brands
  unicode: f4ea
creative-commons-nd:
  changes:
    - 5.0.11
  label: Creative Commons No Derivative Works
  search:
    terms: []
  styles:
    - brands
  unicode: f4eb
creative-commons-pd:
  changes:
    - 5.0.11
  label: Creative Commons Public Domain
  search:
    terms: []
  styles:
    - brands
  unicode: f4ec
creative-commons-pd-alt:
  changes:
    - 5.0.11
  label: Creative Commons Public Domain Alternate
  search:
    terms: []
  styles:
    - brands
  unicode: f4ed
creative-commons-remix:
  changes:
    - 5.0.11
  label: Creative Commons Remix
  search:
    terms: []
  styles:
    - brands
  unicode: f4ee
creative-commons-sa:
  changes:
    - 5.0.11
  label: Creative Commons Share Alike
  search:
    terms: []
  styles:
    - brands
  unicode: f4ef
creative-commons-sampling:
  changes:
    - 5.0.11
  label: Creative Commons Sampling
  search:
    terms: []
  styles:
    - brands
  unicode: f4f0
creative-commons-sampling-plus:
  changes:
    - 5.0.11
  label: Creative Commons Sampling +
  search:
    terms: []
  styles:
    - brands
  unicode: f4f1
creative-commons-share:
  changes:
    - 5.0.11
  label: Creative Commons Share
  search:
    terms: []
  styles:
    - brands
  unicode: f4f2
creative-commons-zero:
  changes:
    - 5.0.11
    - 5.4.0
  label: Creative Commons CC0
  search:
    terms: []
  styles:
    - brands
  unicode: f4f3
credit-card:
  changes:
    - '2'
    - 5.0.0
  label: Credit Card
  search:
    terms:
      - buy
      - checkout
      - credit-card-alt
      - debit
      - money
      - payment
      - purchase
  styles:
    - solid
    - regular
  unicode: f09d
critical-role:
  changes:
    - 5.4.0
  label: Critical Role
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - game
      - gaming
      - tabletop
  styles:
    - brands
  unicode: f6c9
crop:
  changes:
    - '3.1'
    - 5.0.0
    - 5.1.0
  label: crop
  search:
    terms:
      - design
  styles:
    - solid
  unicode: f125
crop-alt:
  changes:
    - 5.1.0
  label: Alternate Crop
  search:
    terms: []
  styles:
    - solid
  unicode: f565
cross:
  changes:
    - 5.3.0
  label: Cross
  search:
    terms:
      - catholicism
      - christianity
  styles:
    - solid
  unicode: f654
crosshairs:
  changes:
    - '1'
    - 5.0.0
  label: Crosshairs
  search:
    terms:
      - gpd
      - picker
      - position
  styles:
    - solid
  unicode: f05b
crow:
  changes:
    - 5.0.13
  label: Crow
  search:
    terms:
      - bird
      - bullfrog
      - fauna
      - halloween
      - holiday
      - toad
  styles:
    - solid
  unicode: f520
crown:
  changes:
    - 5.0.13
  label: Crown
  search:
    terms: []
  styles:
    - solid
  unicode: f521
css3:
  changes:
    - '3.1'
    - 5.0.0
  label: CSS 3 Logo
  search:
    terms:
      - code
  styles:
    - brands
  unicode: f13c
css3-alt:
  changes:
    - 5.0.0
  label: Alternate CSS3 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f38b
cube:
  changes:
    - '4.1'
    - 5.0.0
  label: Cube
  search:
    terms:
      - package
  styles:
    - solid
  unicode: f1b2
cubes:
  changes:
    - '4.1'
    - 5.0.0
  label: Cubes
  search:
    terms:
      - packages
  styles:
    - solid
  unicode: f1b3
cut:
  changes:
    - '2'
    - 5.0.0
    - 5.1.0
  label: Cut
  search:
    terms:
      - scissors
  styles:
    - solid
  unicode: f0c4
cuttlefish:
  changes:
    - 5.0.0
  label: Cuttlefish
  search:
    terms: []
  styles:
    - brands
  unicode: f38c
d-and-d:
  changes:
    - 5.0.0
  label: Dungeons & Dragons
  search:
    terms: []
  styles:
    - brands
  unicode: f38d
d-and-d-beyond:
  changes:
    - 5.4.0
  label: D&D Beyond
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - gaming
      - tabletop
  styles:
    - brands
  unicode: f6ca
dashcube:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
  label: DashCube
  search:
    terms: []
  styles:
    - brands
  unicode: f210
database:
  changes:
    - '4.1'
    - 5.0.0
  label: Database
  search:
    terms: []
  styles:
    - solid
  unicode: f1c0
deaf:
  changes:
    - '4.6'
    - 5.0.0
  label: Deaf
  search:
    terms: []
  styles:
    - solid
  unicode: f2a4
delicious:
  changes:
    - '4.1'
    - 5.0.0
  label: Delicious Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a5
democrat:
  changes:
    - 5.5.0
  label: Democrat
  search:
    terms:
      - american
      - democratic party
      - donkey
      - election
      - left
      - left-wing
      - liberal
      - politics
      - usa
  styles:
    - solid
  unicode: f747
deploydog:
  changes:
    - 5.0.0
  label: deploy.dog
  search:
    terms: []
  styles:
    - brands
  unicode: f38e
deskpro:
  changes:
    - 5.0.0
  label: Deskpro
  search:
    terms: []
  styles:
    - brands
  unicode: f38f
desktop:
  changes:
    - '3'
    - 5.0.0
  label: Desktop
  search:
    terms:
      - computer
      - cpu
      - demo
      - desktop
      - device
      - machine
      - monitor
      - pc
      - screen
  styles:
    - solid
  unicode: f108
dev:
  changes:
    - 5.4.0
  label: DEV
  search:
    terms: []
  styles:
    - brands
  unicode: f6cc
deviantart:
  changes:
    - '4.1'
    - 5.0.0
  label: deviantART
  search:
    terms: []
  styles:
    - brands
  unicode: f1bd
dharmachakra:
  changes:
    - 5.3.0
  label: Dharmachakra
  search:
    terms:
      - buddhism
      - buddhist
      - wheel of dharma
  styles:
    - solid
  unicode: f655
diagnoses:
  changes:
    - 5.0.7
  label: Diagnoses
  search:
    terms: []
  styles:
    - solid
  unicode: f470
dice:
  changes:
    - 5.0.13
  label: Dice
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f522
dice-d20:
  changes:
    - 5.4.0
  label: Dice D20
  search:
    terms:
      - Dungeons & Dragons
      - chance
      - d&d
      - dnd
      - fantasy
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f6cf
dice-d6:
  changes:
    - 5.4.0
  label: Dice D6
  search:
    terms:
      - Dungeons & Dragons
      - chance
      - d&d
      - dnd
      - fantasy
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f6d1
dice-five:
  changes:
    - 5.0.13
  label: Dice Five
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f523
dice-four:
  changes:
    - 5.0.13
  label: Dice Four
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f524
dice-one:
  changes:
    - 5.0.13
  label: Dice One
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f525
dice-six:
  changes:
    - 5.0.13
  label: Dice Six
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f526
dice-three:
  changes:
    - 5.0.13
  label: Dice Three
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f527
dice-two:
  changes:
    - 5.0.13
  label: Dice Two
  search:
    terms:
      - chance
      - gambling
      - game
      - roll
  styles:
    - solid
  unicode: f528
digg:
  changes:
    - '4.1'
    - 5.0.0
  label: Digg Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a6
digital-ocean:
  changes:
    - 5.0.0
  label: Digital Ocean
  search:
    terms: []
  styles:
    - brands
  unicode: f391
digital-tachograph:
  changes:
    - 5.1.0
  label: Digital Tachograph
  search:
    terms: []
  styles:
    - solid
  unicode: f566
directions:
  changes:
    - 5.2.0
  label: Directions
  search:
    terms: []
  styles:
    - solid
  unicode: f5eb
discord:
  changes:
    - 5.0.0
  label: Discord
  search:
    terms: []
  styles:
    - brands
  unicode: f392
discourse:
  changes:
    - 5.0.0
    - 5.0.3
  label: Discourse
  search:
    terms: []
  styles:
    - brands
  unicode: f393
divide:
  changes:
    - 5.0.13
  label: Divide
  search:
    terms: []
  styles:
    - solid
  unicode: f529
dizzy:
  changes:
    - 5.1.0
  label: Dizzy Face
  search:
    terms:
      - dazed
      - disapprove
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f567
dna:
  changes:
    - 5.0.7
    - 5.0.10
  label: DNA
  search:
    terms:
      - double helix
      - helix
  styles:
    - solid
  unicode: f471
dochub:
  changes:
    - 5.0.0
  label: DocHub
  search:
    terms: []
  styles:
    - brands
  unicode: f394
docker:
  changes:
    - 5.0.0
  label: Docker
  search:
    terms: []
  styles:
    - brands
  unicode: f395
dog:
  changes:
    - 5.4.0
  label: Dog
  search:
    terms:
      - canine
      - fauna
      - mammmal
      - pet
      - pooch
      - puppy
      - woof
  styles:
    - solid
  unicode: f6d3
dollar-sign:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.9
  label: Dollar Sign
  search:
    terms:
      - $
      - dollar-sign
      - money
      - price
      - usd
  styles:
    - solid
  unicode: f155
dolly:
  changes:
    - 5.0.7
  label: Dolly
  search:
    terms: []
  styles:
    - solid
  unicode: f472
dolly-flatbed:
  changes:
    - 5.0.7
  label: Dolly Flatbed
  search:
    terms: []
  styles:
    - solid
  unicode: f474
donate:
  changes:
    - 5.0.9
  label: Donate
  search:
    terms:
      - generosity
      - give
  styles:
    - solid
  unicode: f4b9
door-closed:
  changes:
    - 5.0.13
  label: Door Closed
  search:
    terms: []
  styles:
    - solid
  unicode: f52a
door-open:
  changes:
    - 5.0.13
  label: Door Open
  search:
    terms: []
  styles:
    - solid
  unicode: f52b
dot-circle:
  changes:
    - '4'
    - 5.0.0
  label: Dot Circle
  search:
    terms:
      - bullseye
      - notification
      - target
  styles:
    - solid
    - regular
  unicode: f192
dove:
  changes:
    - 5.0.9
  label: Dove
  search:
    terms:
      - bird
      - fauna
      - flying
      - peace
  styles:
    - solid
  unicode: f4ba
download:
  changes:
    - '1'
    - 5.0.0
  label: Download
  search:
    terms:
      - import
  styles:
    - solid
  unicode: f019
draft2digital:
  changes:
    - 5.0.0
  label: Draft2digital
  search:
    terms: []
  styles:
    - brands
  unicode: f396
drafting-compass:
  changes:
    - 5.1.0
  label: Drafting Compass
  search:
    terms:
      - mechanical drawing
      - plot
      - plotting
  styles:
    - solid
  unicode: f568
dragon:
  changes:
    - 5.4.0
  label: Dragon
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
  styles:
    - solid
  unicode: f6d5
draw-polygon:
  changes:
    - 5.2.0
  label: Draw Polygon
  search:
    terms: []
  styles:
    - solid
  unicode: f5ee
dribbble:
  changes:
    - 5.0.0
  label: Dribbble
  search:
    terms: []
  styles:
    - brands
  unicode: f17d
dribbble-square:
  changes:
    - 5.0.0
  label: Dribbble Square
  search:
    terms: []
  styles:
    - brands
  unicode: f397
dropbox:
  changes:
    - '3.2'
    - 5.0.0
    - 5.0.1
  label: Dropbox
  search:
    terms: []
  styles:
    - brands
  unicode: f16b
drum:
  changes:
    - 5.1.0
  label: Drum
  search:
    terms:
      - instrument
      - music
      - percussion
      - snare
      - sound
  styles:
    - solid
  unicode: f569
drum-steelpan:
  changes:
    - 5.1.0
  label: Drum Steelpan
  search:
    terms:
      - calypso
      - instrument
      - music
      - percussion
      - reggae
      - snare
      - sound
      - steel
      - tropical
  styles:
    - solid
  unicode: f56a
drumstick-bite:
  changes:
    - 5.4.0
  label: Drumstick with Bite Taken Out
  search:
    terms: []
  styles:
    - solid
  unicode: f6d7
drupal:
  changes:
    - '4.1'
    - 5.0.0
  label: Drupal Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a9
dumbbell:
  changes:
    - 5.0.5
  label: Dumbbell
  search:
    terms:
      - exercise
      - gym
      - strength
      - weight
      - weight-lifting
  styles:
    - solid
  unicode: f44b
dungeon:
  changes:
    - 5.4.0
  label: Dungeon
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - door
      - entrance
      - fantasy
      - gate
  styles:
    - solid
  unicode: f6d9
dyalog:
  changes:
    - 5.0.0
  label: Dyalog
  search:
    terms: []
  styles:
    - brands
  unicode: f399
earlybirds:
  changes:
    - 5.0.0
  label: Earlybirds
  search:
    terms: []
  styles:
    - brands
  unicode: f39a
ebay:
  changes:
    - 5.0.11
  label: eBay
  search:
    terms: []
  styles:
    - brands
  unicode: f4f4
edge:
  changes:
    - '4.5'
    - 5.0.0
  label: Edge Browser
  search:
    terms:
      - browser
      - ie
  styles:
    - brands
  unicode: f282
edit:
  changes:
    - '1'
    - 5.0.0
  label: Edit
  search:
    terms:
      - edit
      - pen
      - pencil
      - update
      - write
  styles:
    - solid
    - regular
  unicode: f044
eject:
  changes:
    - '1'
    - 5.0.0
  label: eject
  search:
    terms: []
  styles:
    - solid
  unicode: f052
elementor:
  changes:
    - 5.0.3
  label: Elementor
  search:
    terms: []
  styles:
    - brands
  unicode: f430
ellipsis-h:
  changes:
    - '3.1'
    - 5.0.0
  label: Horizontal Ellipsis
  search:
    terms:
      - dots
      - drag
      - kebab
      - list
      - menu
      - nav
      - navigation
      - ol
      - reorder
      - settings
      - ul
  styles:
    - solid
  unicode: f141
ellipsis-v:
  changes:
    - '3.1'
    - 5.0.0
  label: Vertical Ellipsis
  search:
    terms:
      - dots
      - drag
      - kebab
      - list
      - menu
      - nav
      - navigation
      - ol
      - reorder
      - settings
      - ul
  styles:
    - solid
  unicode: f142
ello:
  changes:
    - 5.2.0
  label: Ello
  search:
    terms: []
  styles:
    - brands
  unicode: f5f1
ember:
  changes:
    - 5.0.0
    - 5.0.3
  label: Ember
  search:
    terms: []
  styles:
    - brands
  unicode: f423
empire:
  changes:
    - '4.1'
    - 5.0.0
  label: Galactic Empire
  search:
    terms: []
  styles:
    - brands
  unicode: f1d1
envelope:
  changes:
    - '2'
    - 5.0.0
  label: Envelope
  search:
    terms:
      - e-mail
      - email
      - letter
      - mail
      - message
      - notification
      - support
  styles:
    - solid
    - regular
  unicode: f0e0
envelope-open:
  changes:
    - '4.7'
    - 5.0.0
  label: Envelope Open
  search:
    terms:
      - e-mail
      - email
      - letter
      - mail
      - message
      - notification
      - support
  styles:
    - solid
    - regular
  unicode: f2b6
envelope-open-text:
  changes:
    - 5.3.0
  label: Envelope Open-text
  search:
    terms: []
  styles:
    - solid
  unicode: f658
envelope-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Envelope Square
  search:
    terms:
      - e-mail
      - email
      - letter
      - mail
      - message
      - notification
      - support
  styles:
    - solid
  unicode: f199
envira:
  changes:
    - '4.6'
    - 5.0.0
  label: Envira Gallery
  search:
    terms:
      - leaf
  styles:
    - brands
  unicode: f299
equals:
  changes:
    - 5.0.13
  label: Equals
  search:
    terms: []
  styles:
    - solid
  unicode: f52c
eraser:
  changes:
    - '3.1'
    - 5.0.0
  label: eraser
  search:
    terms:
      - delete
      - remove
  styles:
    - solid
  unicode: f12d
erlang:
  changes:
    - 5.0.0
    - 5.0.3
  label: Erlang
  search:
    terms: []
  styles:
    - brands
  unicode: f39d
ethereum:
  changes:
    - 5.0.2
  label: Ethereum
  search:
    terms: []
  styles:
    - brands
  unicode: f42e
etsy:
  changes:
    - '4.7'
    - 5.0.0
  label: Etsy
  search:
    terms: []
  styles:
    - brands
  unicode: f2d7
euro-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Euro Sign
  search:
    terms:
      - eur
  styles:
    - solid
  unicode: f153
exchange-alt:
  changes:
    - 5.0.0
  label: Alternate Exchange
  search:
    terms:
      - arrow
      - arrows
      - exchange
      - reciprocate
      - return
      - swap
      - transfer
  styles:
    - solid
  unicode: f362
exclamation:
  changes:
    - '3.1'
    - 5.0.0
  label: exclamation
  search:
    terms:
      - alert
      - danger
      - error
      - important
      - notice
      - notification
      - notify
      - problem
      - warning
  styles:
    - solid
  unicode: f12a
exclamation-circle:
  changes:
    - '1'
    - 5.0.0
  label: Exclamation Circle
  search:
    terms:
      - alert
      - danger
      - error
      - important
      - notice
      - notification
      - notify
      - problem
      - warning
  styles:
    - solid
  unicode: f06a
exclamation-triangle:
  changes:
    - '1'
    - 5.0.0
  label: Exclamation Triangle
  search:
    terms:
      - alert
      - danger
      - error
      - important
      - notice
      - notification
      - notify
      - problem
      - warning
  styles:
    - solid
  unicode: f071
expand:
  changes:
    - 5.0.0
  label: Expand
  search:
    terms:
      - bigger
      - enlarge
      - resize
  styles:
    - solid
  unicode: f065
expand-arrows-alt:
  changes:
    - 5.0.0
  label: Alternate Expand Arrows
  search:
    terms:
      - arrows-alt
      - bigger
      - enlarge
      - move
      - resize
  styles:
    - solid
  unicode: f31e
expeditedssl:
  changes:
    - '4.4'
    - 5.0.0
  label: ExpeditedSSL
  search:
    terms: []
  styles:
    - brands
  unicode: f23e
external-link-alt:
  changes:
    - 5.0.0
  label: Alternate External Link
  search:
    terms:
      - external-link
      - new
      - open
  styles:
    - solid
  unicode: f35d
external-link-square-alt:
  changes:
    - 5.0.0
  label: Alternate External Link Square
  search:
    terms:
      - external-link-square
      - new
      - open
  styles:
    - solid
  unicode: f360
eye:
  changes:
    - '1'
    - 5.0.0
  label: Eye
  search:
    terms:
      - optic
      - see
      - seen
      - show
      - sight
      - views
      - visible
  styles:
    - solid
    - regular
  unicode: f06e
eye-dropper:
  changes:
    - '4.2'
    - 5.0.0
    - 5.1.0
  label: Eye Dropper
  search:
    terms:
      - eyedropper
  styles:
    - solid
  unicode: f1fb
eye-slash:
  changes:
    - '1'
    - 5.0.0
  label: Eye Slash
  search:
    terms:
      - blind
      - hide
      - show
      - toggle
      - unseen
      - views
      - visible
      - visiblity
  styles:
    - solid
    - regular
  unicode: f070
facebook:
  changes:
    - '2'
    - 5.0.0
  label: Facebook
  search:
    terms:
      - facebook-official
      - social network
  styles:
    - brands
  unicode: f09a
facebook-f:
  changes:
    - 5.0.0
  label: Facebook F
  search:
    terms:
      - facebook
  styles:
    - brands
  unicode: f39e
facebook-messenger:
  changes:
    - 5.0.0
  label: Facebook Messenger
  search:
    terms: []
  styles:
    - brands
  unicode: f39f
facebook-square:
  changes:
    - '1'
    - 5.0.0
  label: Facebook Square
  search:
    terms:
      - social network
  styles:
    - brands
  unicode: f082
fantasy-flight-games:
  changes:
    - 5.4.0
  label: Fantasy Flight-games
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - game
      - gaming
      - tabletop
  styles:
    - brands
  unicode: f6dc
fast-backward:
  changes:
    - '1'
    - 5.0.0
  label: fast-backward
  search:
    terms:
      - beginning
      - first
      - previous
      - rewind
      - start
  styles:
    - solid
  unicode: f049
fast-forward:
  changes:
    - '1'
    - 5.0.0
  label: fast-forward
  search:
    terms:
      - end
      - last
      - next
  styles:
    - solid
  unicode: f050
fax:
  changes:
    - '4.1'
    - 5.0.0
    - 5.3.0
  label: Fax
  search:
    terms: []
  styles:
    - solid
  unicode: f1ac
feather:
  changes:
    - 5.0.13
    - 5.1.0
  label: Feather
  search:
    terms:
      - bird
      - light
      - plucked
      - quill
  styles:
    - solid
  unicode: f52d
feather-alt:
  changes:
    - 5.1.0
  label: Alternate Feather
  search:
    terms:
      - bird
      - light
      - plucked
      - quill
  styles:
    - solid
  unicode: f56b
female:
  changes:
    - '3.2'
    - 5.0.0
  label: Female
  search:
    terms:
      - human
      - person
      - profile
      - user
      - woman
  styles:
    - solid
  unicode: f182
fighter-jet:
  changes:
    - '3'
    - 5.0.0
  label: fighter-jet
  search:
    terms:
      - airplane
      - fast
      - fly
      - goose
      - maverick
      - plane
      - quick
      - top gun
      - transportation
      - travel
  styles:
    - solid
  unicode: f0fb
file:
  changes:
    - '3.2'
    - 5.0.0
  label: File
  search:
    terms:
      - document
      - new
      - page
      - pdf
      - resume
  styles:
    - solid
    - regular
  unicode: f15b
file-alt:
  changes:
    - '3.2'
    - 5.0.0
  label: Alternate File
  search:
    terms:
      - document
      - file-text
      - invoice
      - new
      - page
      - pdf
  styles:
    - solid
    - regular
  unicode: f15c
file-archive:
  changes:
    - '4.1'
    - 5.0.0
  label: Archive File
  search:
    terms:
      - .zip
      - bundle
      - compress
      - compression
      - download
      - zip
  styles:
    - solid
    - regular
  unicode: f1c6
file-audio:
  changes:
    - '4.1'
    - 5.0.0
  label: Audio File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c7
file-code:
  changes:
    - '4.1'
    - 5.0.0
  label: Code File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c9
file-contract:
  changes:
    - 5.1.0
  label: File Contract
  search:
    terms:
      - agreement
      - binding
      - document
      - legal
      - signature
  styles:
    - solid
  unicode: f56c
file-csv:
  changes:
    - 5.4.0
  label: File CSV
  search:
    terms:
      - spreadsheets
  styles:
    - solid
  unicode: f6dd
file-download:
  changes:
    - 5.1.0
  label: File Download
  search:
    terms: []
  styles:
    - solid
  unicode: f56d
file-excel:
  changes:
    - '4.1'
    - 5.0.0
  label: Excel File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c3
file-export:
  changes:
    - 5.1.0
  label: File Export
  search:
    terms: []
  styles:
    - solid
  unicode: f56e
file-image:
  changes:
    - '4.1'
    - 5.0.0
  label: Image File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c5
file-import:
  changes:
    - 5.1.0
  label: File Import
  search:
    terms: []
  styles:
    - solid
  unicode: f56f
file-invoice:
  changes:
    - 5.1.0
  label: File Invoice
  search:
    terms:
      - bill
      - document
      - receipt
  styles:
    - solid
  unicode: f570
file-invoice-dollar:
  changes:
    - 5.1.0
  label: File Invoice with US Dollar
  search:
    terms:
      - $
      - bill
      - document
      - dollar-sign
      - money
      - receipt
      - usd
  styles:
    - solid
  unicode: f571
file-medical:
  changes:
    - 5.0.7
  label: Medical File
  search:
    terms: []
  styles:
    - solid
  unicode: f477
file-medical-alt:
  changes:
    - 5.0.7
  label: Alternate Medical File
  search:
    terms: []
  styles:
    - solid
  unicode: f478
file-pdf:
  changes:
    - '4.1'
    - 5.0.0
  label: PDF File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c1
file-powerpoint:
  changes:
    - '4.1'
    - 5.0.0
  label: Powerpoint File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c4
file-prescription:
  changes:
    - 5.1.0
  label: File Prescription
  search:
    terms:
      - drugs
      - medical
      - medicine
      - rx
  styles:
    - solid
  unicode: f572
file-signature:
  changes:
    - 5.1.0
  label: File Signature
  search:
    terms:
      - John Hancock
      - contract
      - document
      - name
  styles:
    - solid
  unicode: f573
file-upload:
  changes:
    - 5.1.0
  label: File Upload
  search:
    terms: []
  styles:
    - solid
  unicode: f574
file-video:
  changes:
    - '4.1'
    - 5.0.0
  label: Video File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c8
file-word:
  changes:
    - '4.1'
    - 5.0.0
  label: Word File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c2
fill:
  changes:
    - 5.1.0
  label: Fill
  search:
    terms:
      - bucket
      - color
      - paint
      - paint bucket
  styles:
    - solid
  unicode: f575
fill-drip:
  changes:
    - 5.1.0
  label: Fill Drip
  search:
    terms:
      - bucket
      - color
      - drop
      - paint
      - paint bucket
      - spill
  styles:
    - solid
  unicode: f576
film:
  changes:
    - '1'
    - 5.0.0
  label: Film
  search:
    terms:
      - movie
  styles:
    - solid
  unicode: f008
filter:
  changes:
    - '2'
    - 5.0.0
  label: Filter
  search:
    terms:
      - funnel
      - options
  styles:
    - solid
  unicode: f0b0
fingerprint:
  changes:
    - 5.1.0
  label: Fingerprint
  search:
    terms:
      - human
      - id
      - identification
      - lock
      - smudge
      - touch
      - unique
      - unlock
  styles:
    - solid
  unicode: f577
fire:
  changes:
    - '1'
    - 5.0.0
  label: fire
  search:
    terms:
      - caliente
      - flame
      - heat
      - hot
      - popular
  styles:
    - solid
  unicode: f06d
fire-extinguisher:
  changes:
    - '3.1'
    - 5.0.0
  label: fire-extinguisher
  search:
    terms: []
  styles:
    - solid
  unicode: f134
firefox:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.1
  label: Firefox
  search:
    terms:
      - browser
  styles:
    - brands
  unicode: f269
first-aid:
  changes:
    - 5.0.7
  label: First Aid
  search:
    terms: []
  styles:
    - solid
  unicode: f479
first-order:
  changes:
    - '4.6'
    - 5.0.0
  label: First Order
  search:
    terms: []
  styles:
    - brands
  unicode: f2b0
first-order-alt:
  changes:
    - 5.0.12
  label: Alternate First Order
  search:
    terms: []
  styles:
    - brands
  unicode: f50a
firstdraft:
  changes:
    - 5.0.0
  label: firstdraft
  search:
    terms: []
  styles:
    - brands
  unicode: f3a1
fish:
  changes:
    - 5.1.0
  label: Fish
  search:
    terms:
      - fauna
      - gold
      - swimming
  styles:
    - solid
  unicode: f578
fist-raised:
  changes:
    - 5.4.0
  label: Raised Fist
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - hand
      - ki
      - monk
      - resist
      - strength
      - unarmed combat
  styles:
    - solid
  unicode: f6de
flag:
  changes:
    - '1'
    - 5.0.0
  label: flag
  search:
    terms:
      - country
      - notice
      - notification
      - notify
      - pole
      - report
      - symbol
  styles:
    - solid
    - regular
  unicode: f024
flag-checkered:
  changes:
    - '3.1'
    - 5.0.0
  label: flag-checkered
  search:
    terms:
      - notice
      - notification
      - notify
      - pole
      - racing
      - report
      - symbol
  styles:
    - solid
  unicode: f11e
flag-usa:
  changes:
    - 5.5.0
  label: United States of America Flag
  search:
    terms:
      - betsy ross
      - country
      - old glory
      - stars
      - stripes
      - symbol
  styles:
    - solid
  unicode: f74d
flask:
  changes:
    - '2'
    - 5.0.0
  label: Flask
  search:
    terms:
      - beaker
      - experimental
      - labs
      - science
  styles:
    - solid
  unicode: f0c3
flickr:
  changes:
    - '3.2'
    - 5.0.0
  label: Flickr
  search:
    terms: []
  styles:
    - brands
  unicode: f16e
flipboard:
  changes:
    - 5.0.5
    - 5.0.9
  label: Flipboard
  search:
    terms: []
  styles:
    - brands
  unicode: f44d
flushed:
  changes:
    - 5.1.0
  label: Flushed Face
  search:
    terms:
      - embarrassed
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f579
fly:
  changes:
    - 5.0.0
  label: Fly
  search:
    terms: []
  styles:
    - brands
  unicode: f417
folder:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: Folder
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f07b
folder-minus:
  changes:
    - 5.3.0
  label: Folder Minus
  search:
    terms:
      - archive
      - delete
      - negative
      - remove
  styles:
    - solid
  unicode: f65d
folder-open:
  changes:
    - '1'
    - 5.0.0
  label: Folder Open
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f07c
folder-plus:
  changes:
    - 5.3.0
  label: Folder Plus
  search:
    terms:
      - add
      - create
      - new
      - positive
  styles:
    - solid
  unicode: f65e
font:
  changes:
    - '1'
    - 5.0.0
  label: font
  search:
    terms:
      - text
  styles:
    - solid
  unicode: f031
font-awesome:
  changes:
    - '4.6'
    - 5.0.0
  label: Font Awesome
  search:
    terms:
      - meanpath
  styles:
    - brands
  unicode: f2b4
font-awesome-alt:
  changes:
    - 5.0.0
  label: Alternate Font Awesome
  search:
    terms: []
  styles:
    - brands
  unicode: f35c
font-awesome-flag:
  changes:
    - 5.0.0
    - 5.0.1
  label: Font Awesome Flag
  search:
    terms: []
  styles:
    - brands
  unicode: f425
font-awesome-logo-full:
  changes:
    - 5.0.11
  label: Font Awesome Full Logo
  ligatures:
    - Font Awesome
  private: true
  search:
    terms: []
  styles:
    - regular
    - solid
    - brands
  unicode: f4e6
fonticons:
  changes:
    - '4.4'
    - 5.0.0
  label: Fonticons
  search:
    terms: []
  styles:
    - brands
  unicode: f280
fonticons-fi:
  changes:
    - 5.0.0
  label: Fonticons Fi
  search:
    terms: []
  styles:
    - brands
  unicode: f3a2
football-ball:
  changes:
    - 5.0.5
  label: Football Ball
  search:
    terms:
      - fall
      - pigskin
      - seasonal
  styles:
    - solid
  unicode: f44e
fort-awesome:
  changes:
    - '4.5'
    - 5.0.0
    - 5.0.3
  label: Fort Awesome
  search:
    terms:
      - castle
  styles:
    - brands
  unicode: f286
fort-awesome-alt:
  changes:
    - 5.0.0
  label: Alternate Fort Awesome
  search:
    terms:
      - castle
  styles:
    - brands
  unicode: f3a3
forumbee:
  changes:
    - '4.3'
    - 5.0.0
  label: Forumbee
  search:
    terms: []
  styles:
    - brands
  unicode: f211
forward:
  changes:
    - '1'
    - 5.0.0
  label: forward
  search:
    terms:
      - forward
      - next
  styles:
    - solid
  unicode: f04e
foursquare:
  changes:
    - '3.2'
    - 5.0.0
  label: Foursquare
  search:
    terms: []
  styles:
    - brands
  unicode: f180
free-code-camp:
  changes:
    - '4.7'
    - 5.0.0
  label: Free Code Camp
  search:
    terms: []
  styles:
    - brands
  unicode: f2c5
freebsd:
  changes:
    - 5.0.0
  label: FreeBSD
  search:
    terms: []
  styles:
    - brands
  unicode: f3a4
frog:
  changes:
    - 5.0.13
  label: Frog
  search:
    terms:
      - amphibian
      - bullfrog
      - fauna
      - hop
      - kermit
      - kiss
      - prince
      - ribbit
      - toad
      - wart
  styles:
    - solid
  unicode: f52e
frown:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.9
    - 5.1.0
  label: Frowning Face
  search:
    terms:
      - disapprove
      - emoticon
      - face
      - rating
      - sad
  styles:
    - solid
    - regular
  unicode: f119
frown-open:
  changes:
    - 5.1.0
  label: Frowning Face With Open Mouth
  search:
    terms:
      - disapprove
      - emoticon
      - face
      - rating
      - sad
  styles:
    - solid
    - regular
  unicode: f57a
fulcrum:
  changes:
    - 5.0.12
  label: Fulcrum
  search:
    terms: []
  styles:
    - brands
  unicode: f50b
funnel-dollar:
  changes:
    - 5.3.0
  label: Funnel Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f662
futbol:
  changes:
    - '4.2'
    - 5.0.0
    - 5.0.5
  label: Futbol
  search:
    terms:
      - ball
      - football
      - soccer
  styles:
    - solid
    - regular
  unicode: f1e3
galactic-republic:
  changes:
    - 5.0.12
  label: Galactic Republic
  search:
    terms:
      - politics
      - star wars
  styles:
    - brands
  unicode: f50c
galactic-senate:
  changes:
    - 5.0.12
  label: Galactic Senate
  search:
    terms:
      - star wars
  styles:
    - brands
  unicode: f50d
gamepad:
  changes:
    - '3.1'
    - 5.0.0
  label: Gamepad
  search:
    terms:
      - controller
  styles:
    - solid
  unicode: f11b
gas-pump:
  changes:
    - 5.0.13
  label: Gas Pump
  search:
    terms: []
  styles:
    - solid
  unicode: f52f
gavel:
  changes:
    - '2'
    - 5.0.0
  label: Gavel
  search:
    terms:
      - hammer
      - judge
      - lawyer
      - opinion
  styles:
    - solid
  unicode: f0e3
gem:
  changes:
    - 5.0.0
  label: Gem
  search:
    terms:
      - diamond
  styles:
    - solid
    - regular
  unicode: f3a5
genderless:
  changes:
    - '4.4'
    - 5.0.0
  label: Genderless
  search:
    terms: []
  styles:
    - solid
  unicode: f22d
get-pocket:
  changes:
    - '4.4'
    - 5.0.0
  label: Get Pocket
  search:
    terms: []
  styles:
    - brands
  unicode: f265
gg:
  changes:
    - '4.4'
    - 5.0.0
  label: GG Currency
  search:
    terms: []
  styles:
    - brands
  unicode: f260
gg-circle:
  changes:
    - '4.4'
    - 5.0.0
  label: GG Currency Circle
  search:
    terms: []
  styles:
    - brands
  unicode: f261
ghost:
  changes:
    - 5.4.0
  label: Ghost
  search:
    terms:
      - apparition
      - blinky
      - clyde
      - floating
      - halloween
      - holiday
      - inky
      - pinky
      - spirit
  styles:
    - solid
  unicode: f6e2
gift:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: gift
  search:
    terms:
      - generosity
      - giving
      - party
      - present
      - wrapped
  styles:
    - solid
  unicode: f06b
git:
  changes:
    - '4.1'
    - 5.0.0
  label: Git
  search:
    terms: []
  styles:
    - brands
  unicode: f1d3
git-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Git Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1d2
github:
  changes:
    - '2'
    - 5.0.0
  label: GitHub
  search:
    terms:
      - octocat
  styles:
    - brands
  unicode: f09b
github-alt:
  changes:
    - '3'
    - 5.0.0
  label: Alternate GitHub
  search:
    terms:
      - octocat
  styles:
    - brands
  unicode: f113
github-square:
  changes:
    - '1'
    - 5.0.0
  label: GitHub Square
  search:
    terms:
      - octocat
  styles:
    - brands
  unicode: f092
gitkraken:
  changes:
    - 5.0.0
  label: GitKraken
  search:
    terms: []
  styles:
    - brands
  unicode: f3a6
gitlab:
  changes:
    - '4.6'
    - 5.0.0
  label: GitLab
  search:
    terms:
      - Axosoft
  styles:
    - brands
  unicode: f296
gitter:
  changes:
    - 5.0.0
  label: Gitter
  search:
    terms: []
  styles:
    - brands
  unicode: f426
glass-martini:
  changes:
    - '1'
    - 5.0.0
    - 5.1.0
  label: Martini Glass
  search:
    terms:
      - alcohol
      - bar
      - beverage
      - drink
      - glass
      - liquor
      - martini
  styles:
    - solid
  unicode: f000
glass-martini-alt:
  changes:
    - 5.1.0
  label: Alternate Glass Martini
  search:
    terms: []
  styles:
    - solid
  unicode: f57b
glasses:
  changes:
    - 5.0.13
  label: Glasses
  search:
    terms:
      - foureyes
      - hipster
      - nerd
      - reading
      - sight
      - spectacles
  styles:
    - solid
  unicode: f530
glide:
  changes:
    - '4.6'
    - 5.0.0
  label: Glide
  search:
    terms: []
  styles:
    - brands
  unicode: f2a5
glide-g:
  changes:
    - '4.6'
    - 5.0.0
  label: Glide G
  search:
    terms: []
  styles:
    - brands
  unicode: f2a6
globe:
  changes:
    - '2'
    - 5.0.0
    - 5.0.9
  label: Globe
  search:
    terms:
      - all
      - coordinates
      - country
      - earth
      - global
      - gps
      - language
      - localize
      - location
      - map
      - online
      - place
      - planet
      - translate
      - travel
      - world
  styles:
    - solid
  unicode: f0ac
globe-africa:
  changes:
    - 5.1.0
  label: Globe with Africa shown
  search:
    terms:
      - all
      - country
      - earth
      - global
      - gps
      - language
      - localize
      - location
      - map
      - online
      - place
      - planet
      - translate
      - travel
      - world
  styles:
    - solid
  unicode: f57c
globe-americas:
  changes:
    - 5.1.0
  label: Globe with Americas shown
  search:
    terms:
      - all
      - country
      - earth
      - global
      - gps
      - language
      - localize
      - location
      - map
      - online
      - place
      - planet
      - translate
      - travel
      - world
  styles:
    - solid
  unicode: f57d
globe-asia:
  changes:
    - 5.1.0
  label: Globe with Asia shown
  search:
    terms:
      - all
      - country
      - earth
      - global
      - gps
      - language
      - localize
      - location
      - map
      - online
      - place
      - planet
      - translate
      - travel
      - world
  styles:
    - solid
  unicode: f57e
gofore:
  changes:
    - 5.0.0
  label: Gofore
  search:
    terms: []
  styles:
    - brands
  unicode: f3a7
golf-ball:
  changes:
    - 5.0.5
  label: Golf Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f450
goodreads:
  changes:
    - 5.0.0
  label: Goodreads
  search:
    terms: []
  styles:
    - brands
  unicode: f3a8
goodreads-g:
  changes:
    - 5.0.0
  label: Goodreads G
  search:
    terms: []
  styles:
    - brands
  unicode: f3a9
google:
  changes:
    - '4.1'
    - 5.0.0
  label: Google Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a0
google-drive:
  changes:
    - 5.0.0
  label: Google Drive
  search:
    terms: []
  styles:
    - brands
  unicode: f3aa
google-play:
  changes:
    - 5.0.0
  label: Google Play
  search:
    terms: []
  styles:
    - brands
  unicode: f3ab
google-plus:
  changes:
    - '4.6'
    - 5.0.0
  label: Google Plus
  search:
    terms:
      - google-plus-circle
      - google-plus-official
  styles:
    - brands
  unicode: f2b3
google-plus-g:
  changes:
    - '2'
    - 5.0.0
  label: Google Plus G
  search:
    terms:
      - google-plus
      - social network
  styles:
    - brands
  unicode: f0d5
google-plus-square:
  changes:
    - '2'
    - 5.0.0
  label: Google Plus Square
  search:
    terms:
      - social network
  styles:
    - brands
  unicode: f0d4
google-wallet:
  changes:
    - '4.2'
    - 5.0.0
  label: Google Wallet
  search:
    terms: []
  styles:
    - brands
  unicode: f1ee
gopuram:
  changes:
    - 5.3.0
  label: Gopuram
  search:
    terms:
      - building
      - entrance
      - hinduism
      - temple
      - tower
  styles:
    - solid
  unicode: f664
graduation-cap:
  changes:
    - '4.1'
    - 5.0.0
    - 5.2.0
  label: Graduation Cap
  search:
    terms:
      - learning
      - school
      - student
  styles:
    - solid
  unicode: f19d
gratipay:
  changes:
    - '3.2'
    - 5.0.0
  label: Gratipay (Gittip)
  search:
    terms:
      - favorite
      - heart
      - like
      - love
  styles:
    - brands
  unicode: f184
grav:
  changes:
    - '4.7'
    - 5.0.0
  label: Grav
  search:
    terms: []
  styles:
    - brands
  unicode: f2d6
greater-than:
  changes:
    - 5.0.13
  label: Greater Than
  search:
    terms: []
  styles:
    - solid
  unicode: f531
greater-than-equal:
  changes:
    - 5.0.13
  label: Greater Than Equal To
  search:
    terms: []
  styles:
    - solid
  unicode: f532
grimace:
  changes:
    - 5.1.0
  label: Grimacing Face
  search:
    terms:
      - cringe
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f57f
grin:
  changes:
    - 5.1.0
  label: Grinning Face
  search:
    terms:
      - emoticon
      - face
      - laugh
      - smile
  styles:
    - solid
    - regular
  unicode: f580
grin-alt:
  changes:
    - 5.1.0
  label: Alternate Grinning Face
  search:
    terms:
      - emoticon
      - face
      - laugh
      - smile
  styles:
    - solid
    - regular
  unicode: f581
grin-beam:
  changes:
    - 5.1.0
  label: Grinning Face With Smiling Eyes
  search:
    terms:
      - emoticon
      - face
      - laugh
      - smile
  styles:
    - solid
    - regular
  unicode: f582
grin-beam-sweat:
  changes:
    - 5.1.0
  label: Grinning Face With Sweat
  search:
    terms:
      - emoticon
      - face
      - smile
  styles:
    - solid
    - regular
  unicode: f583
grin-hearts:
  changes:
    - 5.1.0
  label: Smiling Face With Heart-Eyes
  search:
    terms:
      - emoticon
      - face
      - love
      - smile
  styles:
    - solid
    - regular
  unicode: f584
grin-squint:
  changes:
    - 5.1.0
  label: Grinning Squinting Face
  search:
    terms:
      - emoticon
      - face
      - laugh
      - smile
  styles:
    - solid
    - regular
  unicode: f585
grin-squint-tears:
  changes:
    - 5.1.0
  label: Rolling on the Floor Laughing
  search:
    terms:
      - emoticon
      - face
      - happy
      - smile
  styles:
    - solid
    - regular
  unicode: f586
grin-stars:
  changes:
    - 5.1.0
  label: Star-Struck
  search:
    terms:
      - emoticon
      - face
      - star-struck
  styles:
    - solid
    - regular
  unicode: f587
grin-tears:
  changes:
    - 5.1.0
  label: Face With Tears of Joy
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f588
grin-tongue:
  changes:
    - 5.1.0
  label: Face With Tongue
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f589
grin-tongue-squint:
  changes:
    - 5.1.0
  label: Squinting Face With Tongue
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f58a
grin-tongue-wink:
  changes:
    - 5.1.0
  label: Winking Face With Tongue
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f58b
grin-wink:
  changes:
    - 5.1.0
    - 5.1.1
  label: Grinning Winking Face
  search:
    terms:
      - emoticon
      - face
      - flirt
      - laugh
      - smile
  styles:
    - solid
    - regular
  unicode: f58c
grip-horizontal:
  changes:
    - 5.1.0
  label: Grip Horizontal
  search:
    terms:
      - affordance
      - drag
      - drop
      - grab
      - handle
  styles:
    - solid
  unicode: f58d
grip-vertical:
  changes:
    - 5.1.0
  label: Grip Vertical
  search:
    terms:
      - affordance
      - drag
      - drop
      - grab
      - handle
  styles:
    - solid
  unicode: f58e
gripfire:
  changes:
    - 5.0.0
  label: 'Gripfire, Inc.'
  search:
    terms: []
  styles:
    - brands
  unicode: f3ac
grunt:
  changes:
    - 5.0.0
  label: Grunt
  search:
    terms: []
  styles:
    - brands
  unicode: f3ad
gulp:
  changes:
    - 5.0.0
  label: Gulp
  search:
    terms: []
  styles:
    - brands
  unicode: f3ae
h-square:
  changes:
    - '3'
    - 5.0.0
  label: H Square
  search:
    terms:
      - hospital
      - hotel
  styles:
    - solid
  unicode: f0fd
hacker-news:
  changes:
    - '4.1'
    - 5.0.0
  label: Hacker News
  search:
    terms: []
  styles:
    - brands
  unicode: f1d4
hacker-news-square:
  changes:
    - 5.0.0
  label: Hacker News Square
  search:
    terms: []
  styles:
    - brands
  unicode: f3af
hackerrank:
  changes:
    - 5.2.0
  label: Hackerrank
  search:
    terms: []
  styles:
    - brands
  unicode: f5f7
hammer:
  changes:
    - 5.4.0
  label: Hammer
  search:
    terms:
      - admin
      - fix
      - repair
      - settings
      - tool
  styles:
    - solid
  unicode: f6e3
hamsa:
  changes:
    - 5.3.0
  label: Hamsa
  search:
    terms:
      - amulet
      - christianity
      - islam
      - jewish
      - judaism
      - muslim
      - protection
  styles:
    - solid
  unicode: f665
hand-holding:
  changes:
    - 5.0.9
  label: Hand Holding
  search:
    terms: []
  styles:
    - solid
  unicode: f4bd
hand-holding-heart:
  changes:
    - 5.0.9
  label: Hand Holding Heart
  search:
    terms: []
  styles:
    - solid
  unicode: f4be
hand-holding-usd:
  changes:
    - 5.0.9
  label: Hand Holding US Dollar
  search:
    terms:
      - $
      - dollar sign
      - donation
      - giving
      - money
      - price
  styles:
    - solid
  unicode: f4c0
hand-lizard:
  changes:
    - '4.4'
    - 5.0.0
  label: Lizard (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f258
hand-paper:
  changes:
    - '4.4'
    - 5.0.0
  label: Paper (Hand)
  search:
    terms:
      - stop
  styles:
    - solid
    - regular
  unicode: f256
hand-peace:
  changes:
    - '4.4'
    - 5.0.0
  label: Peace (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25b
hand-point-down:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Down
  search:
    terms:
      - finger
      - hand-o-down
      - point
  styles:
    - solid
    - regular
  unicode: f0a7
hand-point-left:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Left
  search:
    terms:
      - back
      - finger
      - hand-o-left
      - left
      - point
      - previous
  styles:
    - solid
    - regular
  unicode: f0a5
hand-point-right:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Right
  search:
    terms:
      - finger
      - forward
      - hand-o-right
      - next
      - point
      - right
  styles:
    - solid
    - regular
  unicode: f0a4
hand-point-up:
  changes:
    - '2'
    - 5.0.0
  label: Hand Pointing Up
  search:
    terms:
      - finger
      - hand-o-up
      - point
  styles:
    - solid
    - regular
  unicode: f0a6
hand-pointer:
  changes:
    - '4.4'
    - 5.0.0
  label: Pointer (Hand)
  search:
    terms:
      - select
  styles:
    - solid
    - regular
  unicode: f25a
hand-rock:
  changes:
    - '4.4'
    - 5.0.0
  label: Rock (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f255
hand-scissors:
  changes:
    - '4.4'
    - 5.0.0
  label: Scissors (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f257
hand-spock:
  changes:
    - '4.4'
    - 5.0.0
  label: Spock (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f259
hands:
  changes:
    - 5.0.9
  label: Hands
  search:
    terms: []
  styles:
    - solid
  unicode: f4c2
hands-helping:
  changes:
    - 5.0.9
  label: Helping Hands
  search:
    terms:
      - aid
      - assistance
      - partnership
      - volunteering
  styles:
    - solid
  unicode: f4c4
handshake:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.9
  label: Handshake
  search:
    terms:
      - greeting
      - partnership
  styles:
    - solid
    - regular
  unicode: f2b5
hanukiah:
  changes:
    - 5.4.0
  label: Hanukiah
  search:
    terms:
      - candle
      - hanukkah
      - jewish
      - judaism
      - light
  styles:
    - solid
  unicode: f6e6
hashtag:
  changes:
    - '4.5'
    - 5.0.0
  label: Hashtag
  search:
    terms: []
  styles:
    - solid
  unicode: f292
hat-wizard:
  changes:
    - 5.4.0
  label: Wizard's Hat
  search:
    terms:
      - Dungeons & Dragons
      - buckle
      - cloth
      - clothing
      - d&d
      - dnd
      - fantasy
      - halloween
      - holiday
      - mage
      - magic
      - pointy
      - witch
  styles:
    - solid
  unicode: f6e8
haykal:
  changes:
    - 5.3.0
  label: Haykal
  search:
    terms:
      - bahai
      - bahá'í
      - star
  styles:
    - solid
  unicode: f666
hdd:
  changes:
    - '2'
    - 5.0.0
  label: HDD
  search:
    terms:
      - cpu
      - hard drive
      - harddrive
      - machine
      - save
      - storage
  styles:
    - solid
    - regular
  unicode: f0a0
heading:
  changes:
    - '4.1'
    - 5.0.0
  label: heading
  search:
    terms:
      - header
  styles:
    - solid
  unicode: f1dc
headphones:
  changes:
    - '1'
    - 5.0.0
  label: headphones
  search:
    terms:
      - audio
      - listen
      - music
      - sound
      - speaker
  styles:
    - solid
  unicode: f025
headphones-alt:
  changes:
    - 5.1.0
  label: Alternate Headphones
  search:
    terms:
      - audio
      - listen
      - music
      - sound
      - speaker
  styles:
    - solid
  unicode: f58f
headset:
  changes:
    - 5.1.0
  label: Headset
  search:
    terms:
      - audio
      - gamer
      - gaming
      - listen
      - live chat
      - microphone
      - shot caller
      - sound
      - support
      - telemarketer
  styles:
    - solid
  unicode: f590
heart:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: Heart
  search:
    terms:
      - favorite
      - like
      - love
  styles:
    - solid
    - regular
  unicode: f004
heartbeat:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.7
  label: Heartbeat
  search:
    terms:
      - ekg
      - lifeline
      - vital signs
  styles:
    - solid
  unicode: f21e
helicopter:
  changes:
    - 5.0.13
  label: Helicopter
  search:
    terms:
      - airwolf
      - apache
      - chopper
      - flight
      - fly
  styles:
    - solid
  unicode: f533
highlighter:
  changes:
    - 5.1.0
  label: Highlighter
  search:
    terms:
      - edit
      - marker
      - sharpie
      - update
      - write
  styles:
    - solid
  unicode: f591
hiking:
  changes:
    - 5.4.0
  label: Hiking
  search:
    terms:
      - activity
      - backpack
      - fall
      - fitness
      - outdoors
      - seasonal
      - walking
  styles:
    - solid
  unicode: f6ec
hippo:
  changes:
    - 5.4.0
  label: Hippo
  search:
    terms:
      - fauna
      - hungry
      - mammmal
  styles:
    - solid
  unicode: f6ed
hips:
  changes:
    - 5.0.5
  label: Hips
  search:
    terms: []
  styles:
    - brands
  unicode: f452
hire-a-helper:
  changes:
    - 5.0.0
  label: HireAHelper
  search:
    terms: []
  styles:
    - brands
  unicode: f3b0
history:
  changes:
    - '4.1'
    - 5.0.0
  label: History
  search:
    terms: []
  styles:
    - solid
  unicode: f1da
hockey-puck:
  changes:
    - 5.0.5
  label: Hockey Puck
  search:
    terms: []
  styles:
    - solid
  unicode: f453
home:
  changes:
    - '1'
    - 5.0.0
  label: home
  search:
    terms:
      - house
      - main
  styles:
    - solid
  unicode: f015
hooli:
  changes:
    - 5.0.0
  label: Hooli
  search:
    terms: []
  styles:
    - brands
  unicode: f427
hornbill:
  changes:
    - 5.1.0
  label: Hornbill
  search:
    terms: []
  styles:
    - brands
  unicode: f592
horse:
  changes:
    - 5.4.0
  label: Horse
  search:
    terms:
      - equus
      - fauna
      - mammmal
      - neigh
  styles:
    - solid
  unicode: f6f0
hospital:
  changes:
    - '3'
    - 5.0.0
  label: hospital
  search:
    terms:
      - building
      - emergency room
      - medical center
  styles:
    - solid
    - regular
  unicode: f0f8
hospital-alt:
  changes:
    - 5.0.7
  label: Alternate Hospital
  search:
    terms:
      - building
      - emergency room
      - medical center
  styles:
    - solid
  unicode: f47d
hospital-symbol:
  changes:
    - 5.0.7
  label: Hospital Symbol
  search:
    terms: []
  styles:
    - solid
  unicode: f47e
hot-tub:
  changes:
    - 5.1.0
  label: Hot Tub
  search:
    terms: []
  styles:
    - solid
  unicode: f593
hotel:
  changes:
    - 5.1.0
  label: Hotel
  search:
    terms:
      - building
      - lodging
  styles:
    - solid
  unicode: f594
hotjar:
  changes:
    - 5.0.0
  label: Hotjar
  search:
    terms: []
  styles:
    - brands
  unicode: f3b1
hourglass:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f254
hourglass-end:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass End
  search:
    terms: []
  styles:
    - solid
  unicode: f253
hourglass-half:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass Half
  search:
    terms: []
  styles:
    - solid
  unicode: f252
hourglass-start:
  changes:
    - '4.4'
    - 5.0.0
  label: Hourglass Start
  search:
    terms: []
  styles:
    - solid
  unicode: f251
house-damage:
  changes:
    - 5.4.0
  label: Damaged House
  search:
    terms:
      - devastation
      - home
  styles:
    - solid
  unicode: f6f1
houzz:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.9
  label: Houzz
  search:
    terms: []
  styles:
    - brands
  unicode: f27c
hryvnia:
  changes:
    - 5.4.0
  label: Hryvnia
  search:
    terms:
      - money
  styles:
    - solid
  unicode: f6f2
html5:
  changes:
    - '3.1'
    - 5.0.0
  label: HTML 5 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f13b
hubspot:
  changes:
    - 5.0.0
  label: HubSpot
  search:
    terms: []
  styles:
    - brands
  unicode: f3b2
i-cursor:
  changes:
    - '4.4'
    - 5.0.0
  label: I Beam Cursor
  search:
    terms: []
  styles:
    - solid
  unicode: f246
id-badge:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Identification Badge
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2c1
id-card:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
  label: Identification Card
  search:
    terms:
      - document
      - identification
      - issued
  styles:
    - solid
    - regular
  unicode: f2c2
id-card-alt:
  changes:
    - 5.0.7
  label: Alternate Identification Card
  search:
    terms:
      - demographics
  styles:
    - solid
  unicode: f47f
image:
  changes:
    - '1'
    - 5.0.0
  label: Image
  search:
    terms:
      - album
      - photo
      - picture
  styles:
    - solid
    - regular
  unicode: f03e
images:
  changes:
    - '1'
    - 5.0.0
  label: Images
  search:
    terms:
      - album
      - photo
      - picture
  styles:
    - solid
    - regular
  unicode: f302
imdb:
  changes:
    - '4.7'
    - 5.0.0
  label: IMDB
  search:
    terms: []
  styles:
    - brands
  unicode: f2d8
inbox:
  changes:
    - '1'
    - 5.0.0
  label: inbox
  search:
    terms: []
  styles:
    - solid
  unicode: f01c
indent:
  changes:
    - '1'
    - 5.0.0
  label: Indent
  search:
    terms: []
  styles:
    - solid
  unicode: f03c
industry:
  changes:
    - '4.4'
    - 5.0.0
  label: Industry
  search:
    terms:
      - factory
      - manufacturing
  styles:
    - solid
  unicode: f275
infinity:
  changes:
    - 5.0.13
    - 5.3.0
  label: Infinity
  search:
    terms: []
  styles:
    - solid
  unicode: f534
info:
  changes:
    - '3.1'
    - 5.0.0
  label: Info
  search:
    terms:
      - details
      - help
      - information
      - more
  styles:
    - solid
  unicode: f129
info-circle:
  changes:
    - '1'
    - 5.0.0
  label: Info Circle
  search:
    terms:
      - details
      - help
      - information
      - more
  styles:
    - solid
  unicode: f05a
instagram:
  changes:
    - '4.6'
    - 5.0.0
  label: Instagram
  search:
    terms: []
  styles:
    - brands
  unicode: f16d
internet-explorer:
  changes:
    - '4.4'
    - 5.0.0
  label: Internet-explorer
  search:
    terms:
      - browser
      - ie
  styles:
    - brands
  unicode: f26b
ioxhost:
  changes:
    - '4.2'
    - 5.0.0
  label: ioxhost
  search:
    terms: []
  styles:
    - brands
  unicode: f208
italic:
  changes:
    - '1'
    - 5.0.0
  label: italic
  search:
    terms:
      - italics
  styles:
    - solid
  unicode: f033
itunes:
  changes:
    - 5.0.0
  label: iTunes
  search:
    terms: []
  styles:
    - brands
  unicode: f3b4
itunes-note:
  changes:
    - 5.0.0
  label: Itunes Note
  search:
    terms: []
  styles:
    - brands
  unicode: f3b5
java:
  changes:
    - 5.0.10
  label: Java
  search:
    terms: []
  styles:
    - brands
  unicode: f4e4
jedi:
  changes:
    - 5.3.0
  label: Jedi
  search:
    terms:
      - star wars
  styles:
    - solid
  unicode: f669
jedi-order:
  changes:
    - 5.0.12
  label: Jedi Order
  search:
    terms:
      - star wars
  styles:
    - brands
  unicode: f50e
jenkins:
  changes:
    - 5.0.0
  label: Jenkis
  search:
    terms: []
  styles:
    - brands
  unicode: f3b6
joget:
  changes:
    - 5.0.0
  label: Joget
  search:
    terms: []
  styles:
    - brands
  unicode: f3b7
joint:
  changes:
    - 5.1.0
  label: Joint
  search:
    terms:
      - blunt
      - cannabis
      - doobie
      - drugs
      - marijuana
      - roach
      - smoke
      - smoking
      - spliff
  styles:
    - solid
  unicode: f595
joomla:
  changes:
    - '4.1'
    - 5.0.0
  label: Joomla Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1aa
journal-whills:
  changes:
    - 5.3.0
  label: Journal of the Whills
  search:
    terms:
      - book
      - jedi
      - star wars
      - the force
  styles:
    - solid
  unicode: f66a
js:
  changes:
    - 5.0.0
  label: JavaScript (JS)
  search:
    terms: []
  styles:
    - brands
  unicode: f3b8
js-square:
  changes:
    - 5.0.0
    - 5.0.3
  label: JavaScript (JS) Square
  search:
    terms: []
  styles:
    - brands
  unicode: f3b9
jsfiddle:
  changes:
    - '4.1'
    - 5.0.0
  label: jsFiddle
  search:
    terms: []
  styles:
    - brands
  unicode: f1cc
kaaba:
  changes:
    - 5.3.0
  label: Kaaba
  search:
    terms:
      - building
      - cube
      - islam
      - muslim
  styles:
    - solid
  unicode: f66b
kaggle:
  changes:
    - 5.2.0
  label: Kaggle
  search:
    terms: []
  styles:
    - brands
  unicode: f5fa
key:
  changes:
    - '1'
    - 5.0.0
  label: key
  search:
    terms:
      - password
      - unlock
  styles:
    - solid
  unicode: f084
keybase:
  changes:
    - 5.0.11
  label: Keybase
  search:
    terms: []
  styles:
    - brands
  unicode: f4f5
keyboard:
  changes:
    - '3.1'
    - 5.0.0
  label: Keyboard
  search:
    terms:
      - input
      - type
  styles:
    - solid
    - regular
  unicode: f11c
keycdn:
  changes:
    - 5.0.0
  label: KeyCDN
  search:
    terms: []
  styles:
    - brands
  unicode: f3ba
khanda:
  changes:
    - 5.3.0
  label: Khanda
  search:
    terms:
      - chakkar
      - sikh
      - sikhism
      - sword
  styles:
    - solid
  unicode: f66d
kickstarter:
  changes:
    - 5.0.0
  label: Kickstarter
  search:
    terms: []
  styles:
    - brands
  unicode: f3bb
kickstarter-k:
  changes:
    - 5.0.0
  label: Kickstarter K
  search:
    terms: []
  styles:
    - brands
  unicode: f3bc
kiss:
  changes:
    - 5.1.0
    - 5.1.1
  label: Kissing Face
  search:
    terms:
      - beso
      - emoticon
      - face
      - love
      - smooch
  styles:
    - solid
    - regular
  unicode: f596
kiss-beam:
  changes:
    - 5.1.0
  label: Kissing Face With Smiling Eyes
  search:
    terms:
      - beso
      - emoticon
      - face
      - love
      - smooch
  styles:
    - solid
    - regular
  unicode: f597
kiss-wink-heart:
  changes:
    - 5.1.0
  label: Face Blowing a Kiss
  search:
    terms:
      - beso
      - emoticon
      - face
      - love
      - smooch
  styles:
    - solid
    - regular
  unicode: f598
kiwi-bird:
  changes:
    - 5.0.13
  label: Kiwi Bird
  search:
    terms:
      - bird
      - fauna
  styles:
    - solid
  unicode: f535
korvue:
  changes:
    - 5.0.2
  label: KORVUE
  search:
    terms: []
  styles:
    - brands
  unicode: f42f
landmark:
  changes:
    - 5.3.0
  label: Landmark
  search:
    terms:
      - building
      - historic
      - memoroable
      - politics
  styles:
    - solid
  unicode: f66f
language:
  changes:
    - '4.1'
    - 5.0.0
  label: Language
  search:
    terms:
      - dialect
      - idiom
      - localize
      - speech
      - translate
      - vernacular
  styles:
    - solid
  unicode: f1ab
laptop:
  changes:
    - '3'
    - 5.0.0
    - 5.2.0
  label: Laptop
  search:
    terms:
      - computer
      - cpu
      - dell
      - demo
      - device
      - dude you're getting
      - mac
      - macbook
      - machine
      - pc
  styles:
    - solid
  unicode: f109
laptop-code:
  changes:
    - 5.2.0
  label: Laptop Code
  search:
    terms: []
  styles:
    - solid
  unicode: f5fc
laravel:
  changes:
    - 5.0.0
    - 5.0.3
  label: Laravel
  search:
    terms: []
  styles:
    - brands
  unicode: f3bd
lastfm:
  changes:
    - '4.2'
    - 5.0.0
  label: last.fm
  search:
    terms: []
  styles:
    - brands
  unicode: f202
lastfm-square:
  changes:
    - '4.2'
    - 5.0.0
    - 5.0.11
  label: last.fm Square
  search:
    terms: []
  styles:
    - brands
  unicode: f203
laugh:
  changes:
    - 5.1.0
  label: Grinning Face With Big Eyes
  search:
    terms:
      - LOL
      - emoticon
      - face
      - laugh
  styles:
    - solid
    - regular
  unicode: f599
laugh-beam:
  changes:
    - 5.1.0
  label: Laugh Face with Beaming Eyes
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f59a
laugh-squint:
  changes:
    - 5.1.0
  label: Laughing Squinting Face
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f59b
laugh-wink:
  changes:
    - 5.1.0
  label: Laughing Winking Face
  search:
    terms:
      - LOL
      - emoticon
      - face
  styles:
    - solid
    - regular
  unicode: f59c
layer-group:
  changes:
    - 5.2.0
  label: Layer Group
  search:
    terms:
      - layers
  styles:
    - solid
  unicode: f5fd
leaf:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: leaf
  search:
    terms:
      - eco
      - flora
      - nature
      - plant
  styles:
    - solid
  unicode: f06c
leanpub:
  changes:
    - '4.3'
    - 5.0.0
  label: Leanpub
  search:
    terms: []
  styles:
    - brands
  unicode: f212
lemon:
  changes:
    - '1'
    - 5.0.0
  label: Lemon
  search:
    terms:
      - food
  styles:
    - solid
    - regular
  unicode: f094
less:
  changes:
    - 5.0.0
  label: Less
  search:
    terms: []
  styles:
    - brands
  unicode: f41d
less-than:
  changes:
    - 5.0.13
  label: Less Than
  search:
    terms: []
  styles:
    - solid
  unicode: f536
less-than-equal:
  changes:
    - 5.0.13
  label: Less Than Equal To
  search:
    terms: []
  styles:
    - solid
  unicode: f537
level-down-alt:
  changes:
    - 5.0.0
  label: Alternate Level Down
  search:
    terms:
      - level-down
  styles:
    - solid
  unicode: f3be
level-up-alt:
  changes:
    - 5.0.0
  label: Alternate Level Up
  search:
    terms:
      - level-up
  styles:
    - solid
  unicode: f3bf
life-ring:
  changes:
    - '4.1'
    - 5.0.0
  label: Life Ring
  search:
    terms:
      - support
  styles:
    - solid
    - regular
  unicode: f1cd
lightbulb:
  changes:
    - '3'
    - 5.0.0
    - 5.3.0
  label: Lightbulb
  search:
    terms:
      - idea
      - inspiration
  styles:
    - solid
    - regular
  unicode: f0eb
line:
  changes:
    - 5.0.0
  label: Line
  search:
    terms: []
  styles:
    - brands
  unicode: f3c0
link:
  changes:
    - '2'
    - 5.0.0
  label: Link
  search:
    terms:
      - chain
  styles:
    - solid
  unicode: f0c1
linkedin:
  changes:
    - '1'
    - 5.0.0
  label: LinkedIn
  search:
    terms:
      - linkedin-square
  styles:
    - brands
  unicode: f08c
linkedin-in:
  changes:
    - '2'
    - 5.0.0
    - 5.4.1
  label: LinkedIn In
  search:
    terms:
      - linkedin
  styles:
    - brands
  unicode: f0e1
linode:
  changes:
    - '4.7'
    - 5.0.0
  label: Linode
  search:
    terms: []
  styles:
    - brands
  unicode: f2b8
linux:
  changes:
    - '3.2'
    - 5.0.0
  label: Linux
  search:
    terms:
      - tux
  styles:
    - brands
  unicode: f17c
lira-sign:
  changes:
    - '4'
    - 5.0.0
  label: Turkish Lira Sign
  search:
    terms:
      - try
      - turkish
  styles:
    - solid
  unicode: f195
list:
  changes:
    - '1'
    - 5.0.0
  label: List
  search:
    terms:
      - checklist
      - completed
      - done
      - finished
      - ol
      - todo
      - ul
  styles:
    - solid
  unicode: f03a
list-alt:
  changes:
    - '1'
    - 5.0.0
  label: Alternate List
  search:
    terms:
      - checklist
      - completed
      - done
      - finished
      - ol
      - todo
      - ul
  styles:
    - solid
    - regular
  unicode: f022
list-ol:
  changes:
    - '2'
    - 5.0.0
  label: list-ol
  search:
    terms:
      - checklist
      - list
      - numbers
      - ol
      - todo
      - ul
  styles:
    - solid
  unicode: f0cb
list-ul:
  changes:
    - '2'
    - 5.0.0
  label: list-ul
  search:
    terms:
      - checklist
      - list
      - ol
      - todo
      - ul
  styles:
    - solid
  unicode: f0ca
location-arrow:
  changes:
    - '3.1'
    - 5.0.0
  label: location-arrow
  search:
    terms:
      - address
      - coordinates
      - gps
      - location
      - map
      - place
      - where
  styles:
    - solid
  unicode: f124
lock:
  changes:
    - '1'
    - 5.0.0
  label: lock
  search:
    terms:
      - admin
      - protect
      - security
  styles:
    - solid
  unicode: f023
lock-open:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.1
  label: Lock Open
  search:
    terms:
      - admin
      - lock
      - open
      - password
      - protect
  styles:
    - solid
  unicode: f3c1
long-arrow-alt-down:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Down
  search:
    terms:
      - long-arrow-down
  styles:
    - solid
  unicode: f309
long-arrow-alt-left:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Left
  search:
    terms:
      - back
      - long-arrow-left
      - previous
  styles:
    - solid
  unicode: f30a
long-arrow-alt-right:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Right
  search:
    terms:
      - long-arrow-right
  styles:
    - solid
  unicode: f30b
long-arrow-alt-up:
  changes:
    - 5.0.0
  label: Alternate Long Arrow Up
  search:
    terms:
      - long-arrow-up
  styles:
    - solid
  unicode: f30c
low-vision:
  changes:
    - '4.6'
    - 5.0.0
  label: Low Vision
  search:
    terms: []
  styles:
    - solid
  unicode: f2a8
luggage-cart:
  changes:
    - 5.1.0
  label: Luggage Cart
  search:
    terms: []
  styles:
    - solid
  unicode: f59d
lyft:
  changes:
    - 5.0.0
  label: lyft
  search:
    terms: []
  styles:
    - brands
  unicode: f3c3
magento:
  changes:
    - 5.0.0
  label: Magento
  search:
    terms: []
  styles:
    - brands
  unicode: f3c4
magic:
  changes:
    - '2'
    - 5.0.0
    - 5.1.0
  label: magic
  search:
    terms:
      - autocomplete
      - automatic
      - mage
      - magic
      - spell
      - witch
      - wizard
  styles:
    - solid
  unicode: f0d0
magnet:
  changes:
    - '1'
    - 5.0.0
  label: magnet
  search:
    terms: []
  styles:
    - solid
  unicode: f076
mail-bulk:
  changes:
    - 5.3.0
  label: Mail Bulk
  search:
    terms: []
  styles:
    - solid
  unicode: f674
mailchimp:
  changes:
    - 5.1.0
  label: Mailchimp
  search:
    terms: []
  styles:
    - brands
  unicode: f59e
male:
  changes:
    - '3.2'
    - 5.0.0
  label: Male
  search:
    terms:
      - human
      - man
      - person
      - profile
      - user
  styles:
    - solid
  unicode: f183
mandalorian:
  changes:
    - 5.0.12
  label: Mandalorian
  search:
    terms: []
  styles:
    - brands
  unicode: f50f
map:
  changes:
    - '4.4'
    - 5.0.0
    - 5.1.0
  label: Map
  search:
    terms:
      - coordinates
      - location
      - paper
      - place
      - travel
  styles:
    - solid
    - regular
  unicode: f279
map-marked:
  changes:
    - 5.1.0
  label: Map Marked
  search:
    terms:
      - address
      - coordinates
      - destination
      - gps
      - localize
      - location
      - map
      - paper
      - pin
      - place
      - point of interest
      - position
      - route
      - travel
      - where
  styles:
    - solid
  unicode: f59f
map-marked-alt:
  changes:
    - 5.1.0
  label: Alternate Map Marked
  search:
    terms:
      - address
      - coordinates
      - destination
      - gps
      - localize
      - location
      - map
      - paper
      - pin
      - place
      - point of interest
      - position
      - route
      - travel
      - where
  styles:
    - solid
  unicode: f5a0
map-marker:
  changes:
    - '1'
    - 5.0.0
  label: map-marker
  search:
    terms:
      - address
      - coordinates
      - gps
      - localize
      - location
      - map
      - pin
      - place
      - position
      - travel
      - where
  styles:
    - solid
  unicode: f041
map-marker-alt:
  changes:
    - 5.0.0
  label: Alternate Map Marker
  search:
    terms:
      - address
      - coordinates
      - gps
      - localize
      - location
      - map
      - pin
      - place
      - position
      - travel
      - where
  styles:
    - solid
  unicode: f3c5
map-pin:
  changes:
    - '4.4'
    - 5.0.0
    - 5.2.0
  label: Map Pin
  search:
    terms:
      - address
      - coordinates
      - gps
      - localize
      - location
      - map
      - marker
      - place
      - position
      - travel
      - where
  styles:
    - solid
  unicode: f276
map-signs:
  changes:
    - '4.4'
    - 5.0.0
    - 5.2.0
  label: Map Signs
  search:
    terms: []
  styles:
    - solid
  unicode: f277
markdown:
  changes:
    - 5.2.0
  label: Markdown
  search:
    terms: []
  styles:
    - brands
  unicode: f60f
marker:
  changes:
    - 5.1.0
  label: Marker
  search:
    terms:
      - edit
      - sharpie
      - update
      - write
  styles:
    - solid
  unicode: f5a1
mars:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars
  search:
    terms:
      - male
  styles:
    - solid
  unicode: f222
mars-double:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Double
  search:
    terms: []
  styles:
    - solid
  unicode: f227
mars-stroke:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Stroke
  search:
    terms: []
  styles:
    - solid
  unicode: f229
mars-stroke-h:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Stroke Horizontal
  search:
    terms: []
  styles:
    - solid
  unicode: f22b
mars-stroke-v:
  changes:
    - '4.3'
    - 5.0.0
  label: Mars Stroke Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f22a
mask:
  changes:
    - 5.4.0
  label: Mask
  search:
    terms:
      - costume
      - disguise
      - halloween
      - holiday
      - secret
      - super hero
  styles:
    - solid
  unicode: f6fa
mastodon:
  changes:
    - 5.0.11
  label: Mastodon
  search:
    terms: []
  styles:
    - brands
  unicode: f4f6
maxcdn:
  changes:
    - '3.1'
    - 5.0.0
  label: MaxCDN
  search:
    terms: []
  styles:
    - brands
  unicode: f136
medal:
  changes:
    - 5.1.0
  label: Medal
  search:
    terms: []
  styles:
    - solid
  unicode: f5a2
medapps:
  changes:
    - 5.0.0
  label: MedApps
  search:
    terms: []
  styles:
    - brands
  unicode: f3c6
medium:
  changes:
    - '4.3'
    - 5.0.0
  label: Medium
  search:
    terms: []
  styles:
    - brands
  unicode: f23a
medium-m:
  changes:
    - 5.0.0
  label: Medium M
  search:
    terms: []
  styles:
    - brands
  unicode: f3c7
medkit:
  changes:
    - '3'
    - 5.0.0
  label: medkit
  search:
    terms:
      - first aid
      - firstaid
      - health
      - help
      - support
  styles:
    - solid
  unicode: f0fa
medrt:
  changes:
    - 5.0.0
  label: MRT
  search:
    terms: []
  styles:
    - brands
  unicode: f3c8
meetup:
  changes:
    - '4.7'
    - 5.0.0
  label: Meetup
  search:
    terms: []
  styles:
    - brands
  unicode: f2e0
megaport:
  changes:
    - 5.1.0
  label: Megaport
  search:
    terms: []
  styles:
    - brands
  unicode: f5a3
meh:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.9
    - 5.1.0
  label: Neutral Face
  search:
    terms:
      - emoticon
      - face
      - neutral
      - rating
  styles:
    - solid
    - regular
  unicode: f11a
meh-blank:
  changes:
    - 5.1.0
  label: Face Without Mouth
  search:
    terms:
      - emoticon
      - face
      - neutral
      - rating
  styles:
    - solid
    - regular
  unicode: f5a4
meh-rolling-eyes:
  changes:
    - 5.1.0
  label: Face With Rolling Eyes
  search:
    terms:
      - emoticon
      - face
      - neutral
      - rating
  styles:
    - solid
    - regular
  unicode: f5a5
memory:
  changes:
    - 5.0.13
  label: Memory
  search:
    terms:
      - DIMM
      - RAM
  styles:
    - solid
  unicode: f538
menorah:
  changes:
    - 5.3.0
    - 5.4.0
  label: Menorah
  search:
    terms:
      - candle
      - hanukkah
      - jewish
      - judaism
      - light
  styles:
    - solid
  unicode: f676
mercury:
  changes:
    - '4.3'
    - 5.0.0
  label: Mercury
  search:
    terms:
      - transgender
  styles:
    - solid
  unicode: f223
meteor:
  changes:
    - 5.5.0
  label: Meteor
  search:
    terms: []
  styles:
    - solid
  unicode: f753
microchip:
  changes:
    - '4.7'
    - 5.0.0
  label: Microchip
  search:
    terms:
      - cpu
      - processor
  styles:
    - solid
  unicode: f2db
microphone:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.13
  label: microphone
  search:
    terms:
      - record
      - sound
      - voice
  styles:
    - solid
  unicode: f130
microphone-alt:
  changes:
    - 5.0.0
    - 5.0.13
  label: Alternate Microphone
  search:
    terms:
      - record
      - sound
      - voice
  styles:
    - solid
  unicode: f3c9
microphone-alt-slash:
  changes:
    - 5.0.13
  label: Alternate Microphone Slash
  search:
    terms:
      - disable
      - mute
      - record
      - sound
      - voice
  styles:
    - solid
  unicode: f539
microphone-slash:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.13
  label: Microphone Slash
  search:
    terms:
      - disable
      - mute
      - record
      - sound
      - voice
  styles:
    - solid
  unicode: f131
microscope:
  changes:
    - 5.2.0
  label: Microscope
  search:
    terms: []
  styles:
    - solid
  unicode: f610
microsoft:
  changes:
    - 5.0.0
  label: Microsoft
  search:
    terms: []
  styles:
    - brands
  unicode: f3ca
minus:
  changes:
    - '1'
    - 5.0.0
  label: minus
  search:
    terms:
      - collapse
      - delete
      - hide
      - minify
      - negative
      - remove
      - trash
  styles:
    - solid
  unicode: f068
minus-circle:
  changes:
    - '1'
    - 5.0.0
  label: Minus Circle
  search:
    terms:
      - delete
      - hide
      - negative
      - remove
      - trash
  styles:
    - solid
  unicode: f056
minus-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Minus Square
  search:
    terms:
      - collapse
      - delete
      - hide
      - minify
      - negative
      - remove
      - trash
  styles:
    - solid
    - regular
  unicode: f146
mix:
  changes:
    - 5.0.0
    - 5.0.3
  label: Mix
  search:
    terms: []
  styles:
    - brands
  unicode: f3cb
mixcloud:
  changes:
    - '4.5'
    - 5.0.0
  label: Mixcloud
  search:
    terms: []
  styles:
    - brands
  unicode: f289
mizuni:
  changes:
    - 5.0.0
  label: Mizuni
  search:
    terms: []
  styles:
    - brands
  unicode: f3cc
mobile:
  changes:
    - '3'
    - 5.0.0
  label: Mobile Phone
  search:
    terms:
      - apple
      - call
      - cell phone
      - cellphone
      - device
      - iphone
      - number
      - screen
      - telephone
      - text
  styles:
    - solid
  unicode: f10b
mobile-alt:
  changes:
    - 5.0.0
  label: Alternate Mobile
  search:
    terms:
      - apple
      - call
      - cell phone
      - cellphone
      - device
      - iphone
      - number
      - screen
      - telephone
      - text
  styles:
    - solid
  unicode: f3cd
modx:
  changes:
    - '4.5'
    - 5.0.0
  label: MODX
  search:
    terms: []
  styles:
    - brands
  unicode: f285
monero:
  changes:
    - 5.0.0
  label: Monero
  search:
    terms: []
  styles:
    - brands
  unicode: f3d0
money-bill:
  changes:
    - '2'
    - 5.0.0
    - 5.0.13
  label: Money Bill
  search:
    terms:
      - buy
      - cash
      - checkout
      - money
      - payment
      - price
      - purchase
  styles:
    - solid
  unicode: f0d6
money-bill-alt:
  changes:
    - 5.0.0
    - 5.0.13
  label: Alternate Money Bill
  search:
    terms:
      - buy
      - cash
      - checkout
      - money
      - payment
      - price
      - purchase
  styles:
    - solid
    - regular
  unicode: f3d1
money-bill-wave:
  changes:
    - 5.0.13
  label: Wavy Money Bill
  search:
    terms: []
  styles:
    - solid
  unicode: f53a
money-bill-wave-alt:
  changes:
    - 5.0.13
  label: Alternate Wavy Money Bill
  search:
    terms: []
  styles:
    - solid
  unicode: f53b
money-check:
  changes:
    - 5.0.13
  label: Money Check
  search:
    terms:
      - bank check
      - cheque
  styles:
    - solid
  unicode: f53c
money-check-alt:
  changes:
    - 5.0.13
  label: Alternate Money Check
  search:
    terms:
      - bank check
      - cheque
  styles:
    - solid
  unicode: f53d
monument:
  changes:
    - 5.1.0
  label: Monument
  search:
    terms:
      - building
      - historic
      - memoroable
  styles:
    - solid
  unicode: f5a6
moon:
  changes:
    - '3.2'
    - 5.0.0
  label: Moon
  search:
    terms:
      - contrast
      - crescent
      - darker
      - lunar
      - night
  styles:
    - solid
    - regular
  unicode: f186
mortar-pestle:
  changes:
    - 5.1.0
  label: Mortar Pestle
  search:
    terms:
      - crush
      - culinary
      - grind
      - medical
      - mix
      - spices
  styles:
    - solid
  unicode: f5a7
mosque:
  changes:
    - 5.3.0
  label: Mosque
  search:
    terms:
      - building
      - islam
      - muslim
  styles:
    - solid
  unicode: f678
motorcycle:
  changes:
    - '4.3'
    - 5.0.0
  label: Motorcycle
  search:
    terms:
      - bike
      - machine
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f21c
mountain:
  changes:
    - 5.4.0
  label: Mountain
  search:
    terms: []
  styles:
    - solid
  unicode: f6fc
mouse-pointer:
  changes:
    - '4.4'
    - 5.0.0
    - 5.0.3
  label: Mouse Pointer
  search:
    terms:
      - select
  styles:
    - solid
  unicode: f245
music:
  changes:
    - '1'
    - 5.0.0
    - 5.2.0
  label: Music
  search:
    terms:
      - note
      - sound
  styles:
    - solid
  unicode: f001
napster:
  changes:
    - 5.0.0
  label: Napster
  search:
    terms: []
  styles:
    - brands
  unicode: f3d2
neos:
  changes:
    - 5.2.0
  label: Neos
  search:
    terms: []
  styles:
    - brands
  unicode: f612
network-wired:
  changes:
    - 5.4.0
  label: Wired Network
  search:
    terms: []
  styles:
    - solid
  unicode: f6ff
neuter:
  changes:
    - '4.3'
    - 5.0.0
  label: Neuter
  search:
    terms: []
  styles:
    - solid
  unicode: f22c
newspaper:
  changes:
    - '4.2'
    - 5.0.0
  label: Newspaper
  search:
    terms:
      - article
      - press
  styles:
    - solid
    - regular
  unicode: f1ea
nimblr:
  changes:
    - 5.1.0
  label: Nimblr
  search:
    terms: []
  styles:
    - brands
  unicode: f5a8
nintendo-switch:
  changes:
    - 5.0.0
  label: Nintendo Switch
  search:
    terms: []
  styles:
    - brands
  unicode: f418
node:
  changes:
    - 5.0.0
  label: Node.js
  search:
    terms: []
  styles:
    - brands
  unicode: f419
node-js:
  changes:
    - 5.0.0
    - 5.0.3
  label: Node.js JS
  search:
    terms: []
  styles:
    - brands
  unicode: f3d3
not-equal:
  changes:
    - 5.0.13
  label: Not Equal
  search:
    terms: []
  styles:
    - solid
  unicode: f53e
notes-medical:
  changes:
    - 5.0.7
  label: Medical Notes
  search:
    terms: []
  styles:
    - solid
  unicode: f481
npm:
  changes:
    - 5.0.0
  label: npm
  search:
    terms: []
  styles:
    - brands
  unicode: f3d4
ns8:
  changes:
    - 5.0.0
  label: NS8
  search:
    terms: []
  styles:
    - brands
  unicode: f3d5
nutritionix:
  changes:
    - 5.0.0
  label: Nutritionix
  search:
    terms: []
  styles:
    - brands
  unicode: f3d6
object-group:
  changes:
    - '4.4'
    - 5.0.0
  label: Object Group
  search:
    terms:
      - design
  styles:
    - solid
    - regular
  unicode: f247
object-ungroup:
  changes:
    - '4.4'
    - 5.0.0
  label: Object Ungroup
  search:
    terms:
      - design
  styles:
    - solid
    - regular
  unicode: f248
odnoklassniki:
  changes:
    - '4.4'
    - 5.0.0
  label: Odnoklassniki
  search:
    terms: []
  styles:
    - brands
  unicode: f263
odnoklassniki-square:
  changes:
    - '4.4'
    - 5.0.0
  label: Odnoklassniki Square
  search:
    terms: []
  styles:
    - brands
  unicode: f264
oil-can:
  changes:
    - 5.2.0
  label: Oil Can
  search:
    terms: []
  styles:
    - solid
  unicode: f613
old-republic:
  changes:
    - 5.0.12
  label: Old Republic
  search:
    terms:
      - politics
      - star wars
  styles:
    - brands
  unicode: f510
om:
  changes:
    - 5.3.0
  label: Om
  search:
    terms:
      - buddhism
      - hinduism
      - jainism
      - mantra
  styles:
    - solid
  unicode: f679
opencart:
  changes:
    - '4.4'
    - 5.0.0
  label: OpenCart
  search:
    terms: []
  styles:
    - brands
  unicode: f23d
openid:
  changes:
    - '4.1'
    - 5.0.0
  label: OpenID
  search:
    terms: []
  styles:
    - brands
  unicode: f19b
opera:
  changes:
    - '4.4'
    - 5.0.0
  label: Opera
  search:
    terms: []
  styles:
    - brands
  unicode: f26a
optin-monster:
  changes:
    - '4.4'
    - 5.0.0
  label: Optin Monster
  search:
    terms: []
  styles:
    - brands
  unicode: f23c
osi:
  changes:
    - 5.0.0
  label: Open Source Initiative
  search:
    terms: []
  styles:
    - brands
  unicode: f41a
otter:
  changes:
    - 5.4.0
  label: Otter
  search:
    terms:
      - fauna
      - mammmal
  styles:
    - solid
  unicode: f700
outdent:
  changes:
    - '1'
    - 5.0.0
  label: Outdent
  search:
    terms: []
  styles:
    - solid
  unicode: f03b
page4:
  changes:
    - 5.0.0
  label: page4 Corporation
  search:
    terms: []
  styles:
    - brands
  unicode: f3d7
pagelines:
  changes:
    - '4'
    - 5.0.0
  label: Pagelines
  search:
    terms:
      - eco
      - flora
      - leaf
      - leaves
      - nature
      - plant
      - tree
  styles:
    - brands
  unicode: f18c
paint-brush:
  changes:
    - '4.2'
    - 5.0.0
    - 5.1.0
  label: Paint Brush
  search:
    terms: []
  styles:
    - solid
  unicode: f1fc
paint-roller:
  changes:
    - 5.1.0
  label: Paint Roller
  search:
    terms:
      - brush
      - painting
      - tool
  styles:
    - solid
  unicode: f5aa
palette:
  changes:
    - 5.0.13
  label: Palette
  search:
    terms:
      - colors
      - painting
  styles:
    - solid
  unicode: f53f
palfed:
  changes:
    - 5.0.0
    - 5.0.3
  label: Palfed
  search:
    terms: []
  styles:
    - brands
  unicode: f3d8
pallet:
  changes:
    - 5.0.7
  label: Pallet
  search:
    terms: []
  styles:
    - solid
  unicode: f482
paper-plane:
  changes:
    - '4.1'
    - 5.0.0
  label: Paper Plane
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1d8
paperclip:
  changes:
    - '2'
    - 5.0.0
  label: Paperclip
  search:
    terms:
      - attachment
  styles:
    - solid
  unicode: f0c6
parachute-box:
  changes:
    - 5.0.9
  label: Parachute Box
  search:
    terms:
      - aid
      - assistance
      - rescue
      - supplies
  styles:
    - solid
  unicode: f4cd
paragraph:
  changes:
    - '4.1'
    - 5.0.0
  label: paragraph
  search:
    terms: []
  styles:
    - solid
  unicode: f1dd
parking:
  changes:
    - 5.0.13
  label: Parking
  search:
    terms: []
  styles:
    - solid
  unicode: f540
passport:
  changes:
    - 5.1.0
  label: Passport
  search:
    terms:
      - document
      - identification
      - issued
  styles:
    - solid
  unicode: f5ab
pastafarianism:
  changes:
    - 5.3.0
  label: Pastafarianism
  search:
    terms:
      - agnosticism
      - atheism
      - flying spaghetti monster
      - fsm
  styles:
    - solid
  unicode: f67b
paste:
  changes:
    - '2'
    - 5.0.0
  label: Paste
  search:
    terms:
      - clipboard
      - copy
  styles:
    - solid
  unicode: f0ea
patreon:
  changes:
    - 5.0.0
    - 5.0.3
  label: Patreon
  search:
    terms: []
  styles:
    - brands
  unicode: f3d9
pause:
  changes:
    - '1'
    - 5.0.0
  label: pause
  search:
    terms:
      - wait
  styles:
    - solid
  unicode: f04c
pause-circle:
  changes:
    - '4.5'
    - 5.0.0
  label: Pause Circle
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f28b
paw:
  changes:
    - '4.1'
    - 5.0.0
  label: Paw
  search:
    terms:
      - animal
      - pet
  styles:
    - solid
  unicode: f1b0
paypal:
  changes:
    - '4.2'
    - 5.0.0
  label: Paypal
  search:
    terms: []
  styles:
    - brands
  unicode: f1ed
peace:
  changes:
    - 5.3.0
  label: Peace
  search:
    terms: []
  styles:
    - solid
  unicode: f67c
pen:
  changes:
    - 5.0.0
    - 5.1.0
  label: Pen
  search:
    terms:
      - design
      - edit
      - update
      - write
  styles:
    - solid
  unicode: f304
pen-alt:
  changes:
    - 5.0.0
    - 5.1.0
  label: Alternate Pen
  search:
    terms:
      - design
      - edit
      - update
      - write
  styles:
    - solid
  unicode: f305
pen-fancy:
  changes:
    - 5.1.0
  label: Pen Fancy
  search:
    terms:
      - design
      - edit
      - fountain pen
      - update
      - write
  styles:
    - solid
  unicode: f5ac
pen-nib:
  changes:
    - 5.1.0
  label: Pen Nib
  search:
    terms:
      - design
      - edit
      - fountain pen
      - update
      - write
  styles:
    - solid
  unicode: f5ad
pen-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Pen Square
  search:
    terms:
      - edit
      - pencil-square
      - update
      - write
  styles:
    - solid
  unicode: f14b
pencil-alt:
  changes:
    - 5.0.0
  label: Alternate Pencil
  search:
    terms:
      - design
      - edit
      - pencil
      - update
      - write
  styles:
    - solid
  unicode: f303
pencil-ruler:
  changes:
    - 5.1.0
  label: Pencil Ruler
  search:
    terms: []
  styles:
    - solid
  unicode: f5ae
penny-arcade:
  changes:
    - 5.4.0
  label: Penny Arcade
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - game
      - gaming
      - pax
      - tabletop
  styles:
    - brands
  unicode: f704
people-carry:
  changes:
    - 5.0.9
  label: People Carry
  search:
    terms:
      - movers
  styles:
    - solid
  unicode: f4ce
percent:
  changes:
    - '4.5'
    - 5.0.0
  label: Percent
  search:
    terms: []
  styles:
    - solid
  unicode: f295
percentage:
  changes:
    - 5.0.13
  label: Percentage
  search:
    terms: []
  styles:
    - solid
  unicode: f541
periscope:
  changes:
    - 5.0.0
  label: Periscope
  search:
    terms: []
  styles:
    - brands
  unicode: f3da
person-booth:
  changes:
    - 5.5.0
  label: Person Entering Booth
  search:
    terms:
      - changing
      - changing room
      - election
      - human
      - person
      - vote
      - voting
  styles:
    - solid
  unicode: f756
phabricator:
  changes:
    - 5.0.0
  label: Phabricator
  search:
    terms: []
  styles:
    - brands
  unicode: f3db
phoenix-framework:
  changes:
    - 5.0.0
    - 5.0.3
  label: Phoenix Framework
  search:
    terms: []
  styles:
    - brands
  unicode: f3dc
phoenix-squadron:
  changes:
    - 5.0.12
  label: Phoenix Squadron
  search:
    terms: []
  styles:
    - brands
  unicode: f511
phone:
  changes:
    - '2'
    - 5.0.0
  label: Phone
  search:
    terms:
      - call
      - earphone
      - number
      - support
      - telephone
      - voice
  styles:
    - solid
  unicode: f095
phone-slash:
  changes:
    - 5.0.0
    - 5.0.9
  label: Phone Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f3dd
phone-square:
  changes:
    - '2'
    - 5.0.0
  label: Phone Square
  search:
    terms:
      - call
      - number
      - support
      - telephone
      - voice
  styles:
    - solid
  unicode: f098
phone-volume:
  changes:
    - '4.6'
    - 5.0.0
    - 5.0.3
  label: Phone Volume
  search:
    terms:
      - telephone
      - volume-control-phone
  styles:
    - solid
  unicode: f2a0
php:
  changes:
    - 5.0.5
  label: PHP
  search:
    terms: []
  styles:
    - brands
  unicode: f457
pied-piper:
  changes:
    - '4.6'
    - 5.0.0
    - 5.0.10
  label: Pied Piper Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f2ae
pied-piper-alt:
  changes:
    - '4.1'
    - 5.0.0
  label: Alternate Pied Piper Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a8
pied-piper-hat:
  changes:
    - 5.0.10
  label: Pied Piper-hat
  search:
    terms:
      - clothing
  styles:
    - brands
  unicode: f4e5
pied-piper-pp:
  changes:
    - '4.1'
    - 5.0.0
  label: Pied Piper PP Logo (Old)
  search:
    terms: []
  styles:
    - brands
  unicode: f1a7
piggy-bank:
  changes:
    - 5.0.9
  label: Piggy Bank
  search:
    terms:
      - save
      - savings
  styles:
    - solid
  unicode: f4d3
pills:
  changes:
    - 5.0.7
  label: Pills
  search:
    terms:
      - drugs
      - medicine
  styles:
    - solid
  unicode: f484
pinterest:
  changes:
    - '2'
    - 5.0.0
  label: Pinterest
  search:
    terms: []
  styles:
    - brands
  unicode: f0d2
pinterest-p:
  changes:
    - '4.3'
    - 5.0.0
  label: Pinterest P
  search:
    terms: []
  styles:
    - brands
  unicode: f231
pinterest-square:
  changes:
    - '2'
    - 5.0.0
  label: Pinterest Square
  search:
    terms: []
  styles:
    - brands
  unicode: f0d3
place-of-worship:
  changes:
    - 5.3.0
  label: Place Of Worship
  search:
    terms: []
  styles:
    - solid
  unicode: f67f
plane:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: plane
  search:
    terms:
      - airplane
      - destination
      - fly
      - location
      - mode
      - travel
      - trip
  styles:
    - solid
  unicode: f072
plane-arrival:
  changes:
    - 5.1.0
  label: Plane Arrival
  search:
    terms:
      - airplane
      - arriving
      - destination
      - fly
      - land
      - landing
      - location
      - mode
      - travel
      - trip
  styles:
    - solid
  unicode: f5af
plane-departure:
  changes:
    - 5.1.0
  label: Plane Departure
  search:
    terms:
      - airplane
      - departing
      - destination
      - fly
      - location
      - mode
      - take off
      - taking off
      - travel
      - trip
  styles:
    - solid
  unicode: f5b0
play:
  changes:
    - '1'
    - 5.0.0
  label: play
  search:
    terms:
      - music
      - playing
      - sound
      - start
  styles:
    - solid
  unicode: f04b
play-circle:
  changes:
    - '3.1'
    - 5.0.0
  label: Play Circle
  search:
    terms:
      - playing
      - start
  styles:
    - solid
    - regular
  unicode: f144
playstation:
  changes:
    - 5.0.0
  label: PlayStation
  search:
    terms: []
  styles:
    - brands
  unicode: f3df
plug:
  changes:
    - '4.2'
    - 5.0.0
  label: Plug
  search:
    terms:
      - connect
      - online
      - power
  styles:
    - solid
  unicode: f1e6
plus:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: plus
  search:
    terms:
      - add
      - create
      - expand
      - new
      - positive
  styles:
    - solid
  unicode: f067
plus-circle:
  changes:
    - '1'
    - 5.0.0
  label: Plus Circle
  search:
    terms:
      - add
      - create
      - expand
      - new
      - positive
  styles:
    - solid
  unicode: f055
plus-square:
  changes:
    - '3'
    - 5.0.0
  label: Plus Square
  search:
    terms:
      - add
      - create
      - expand
      - new
      - positive
  styles:
    - solid
    - regular
  unicode: f0fe
podcast:
  changes:
    - '4.7'
    - 5.0.0
  label: Podcast
  search:
    terms: []
  styles:
    - solid
  unicode: f2ce
poll:
  changes:
    - 5.3.0
  label: Poll
  search:
    terms:
      - results
      - survey
      - vote
      - voting
  styles:
    - solid
  unicode: f681
poll-h:
  changes:
    - 5.3.0
  label: Poll H
  search:
    terms:
      - results
      - survey
      - vote
      - voting
  styles:
    - solid
  unicode: f682
poo:
  changes:
    - 5.0.0
    - 5.0.9
  label: Poo
  search:
    terms: []
  styles:
    - solid
  unicode: f2fe
poo-storm:
  changes:
    - 5.5.0
  label: Poo Storm
  search:
    terms:
      - mess
      - poop
      - shit
  styles:
    - solid
  unicode: f75a
poop:
  changes:
    - 5.2.0
  label: Poop
  search:
    terms: []
  styles:
    - solid
  unicode: f619
portrait:
  changes:
    - 5.0.0
    - 5.0.3
  label: Portrait
  search:
    terms: []
  styles:
    - solid
  unicode: f3e0
pound-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Pound Sign
  search:
    terms:
      - gbp
  styles:
    - solid
  unicode: f154
power-off:
  changes:
    - '1'
    - 5.0.0
  label: Power Off
  search:
    terms:
      - 'on'
      - reboot
      - restart
  styles:
    - solid
  unicode: f011
pray:
  changes:
    - 5.3.0
  label: Pray
  search:
    terms: []
  styles:
    - solid
  unicode: f683
praying-hands:
  changes:
    - 5.3.0
  label: Praying Hands
  search:
    terms: []
  styles:
    - solid
  unicode: f684
prescription:
  changes:
    - 5.1.0
  label: Prescription
  search:
    terms:
      - drugs
      - medical
      - medicine
      - rx
  styles:
    - solid
  unicode: f5b1
prescription-bottle:
  changes:
    - 5.0.7
  label: Prescription Bottle
  search:
    terms:
      - drugs
      - medical
      - medicine
      - rx
  styles:
    - solid
  unicode: f485
prescription-bottle-alt:
  changes:
    - 5.0.7
  label: Alternate Prescription Bottle
  search:
    terms:
      - drugs
      - medical
      - medicine
      - rx
  styles:
    - solid
  unicode: f486
print:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: print
  search:
    terms: []
  styles:
    - solid
  unicode: f02f
procedures:
  changes:
    - 5.0.7
  label: Procedures
  search:
    terms: []
  styles:
    - solid
  unicode: f487
product-hunt:
  changes:
    - '4.5'
    - 5.0.0
  label: Product Hunt
  search:
    terms: []
  styles:
    - brands
  unicode: f288
project-diagram:
  changes:
    - 5.0.13
  label: Project Diagram
  search:
    terms: []
  styles:
    - solid
  unicode: f542
pushed:
  changes:
    - 5.0.0
  label: Pushed
  search:
    terms: []
  styles:
    - brands
  unicode: f3e1
puzzle-piece:
  changes:
    - '3.1'
    - 5.0.0
  label: Puzzle Piece
  search:
    terms:
      - add-on
      - addon
      - section
  styles:
    - solid
  unicode: f12e
python:
  changes:
    - 5.0.0
  label: Python
  search:
    terms: []
  styles:
    - brands
  unicode: f3e2
qq:
  changes:
    - '4.1'
    - 5.0.0
  label: QQ
  search:
    terms: []
  styles:
    - brands
  unicode: f1d6
qrcode:
  changes:
    - '1'
    - 5.0.0
  label: qrcode
  search:
    terms:
      - scan
  styles:
    - solid
  unicode: f029
question:
  changes:
    - '3.1'
    - 5.0.0
  label: Question
  search:
    terms:
      - help
      - information
      - support
      - unknown
  styles:
    - solid
  unicode: f128
question-circle:
  changes:
    - '1'
    - 5.0.0
  label: Question Circle
  search:
    terms:
      - help
      - information
      - support
      - unknown
  styles:
    - solid
    - regular
  unicode: f059
quidditch:
  changes:
    - 5.0.5
  label: Quidditch
  search:
    terms: []
  styles:
    - solid
  unicode: f458
quinscape:
  changes:
    - 5.0.5
  label: QuinScape
  search:
    terms: []
  styles:
    - brands
  unicode: f459
quora:
  changes:
    - '4.7'
    - 5.0.0
  label: Quora
  search:
    terms: []
  styles:
    - brands
  unicode: f2c4
quote-left:
  changes:
    - '3'
    - 5.0.0
    - 5.0.9
  label: quote-left
  search:
    terms: []
  styles:
    - solid
  unicode: f10d
quote-right:
  changes:
    - '3'
    - 5.0.0
    - 5.0.9
  label: quote-right
  search:
    terms: []
  styles:
    - solid
  unicode: f10e
quran:
  changes:
    - 5.3.0
  label: Quran
  search:
    terms:
      - book
      - islam
      - muslim
  styles:
    - solid
  unicode: f687
r-project:
  changes:
    - 5.0.11
    - 5.0.12
  label: R Project
  search:
    terms: []
  styles:
    - brands
  unicode: f4f7
rainbow:
  changes:
    - 5.5.0
  label: Rainbow
  search:
    terms: []
  styles:
    - solid
  unicode: f75b
random:
  changes:
    - '1'
    - 5.0.0
  label: random
  search:
    terms:
      - shuffle
      - sort
  styles:
    - solid
  unicode: f074
ravelry:
  changes:
    - '4.7'
    - 5.0.0
  label: Ravelry
  search:
    terms: []
  styles:
    - brands
  unicode: f2d9
react:
  changes:
    - 5.0.0
  label: React
  search:
    terms: []
  styles:
    - brands
  unicode: f41b
reacteurope:
  changes:
    - 5.5.0
  label: ReactEurope
  search:
    terms: []
  styles:
    - brands
  unicode: f75d
readme:
  changes:
    - 5.0.9
    - 5.0.10
  label: ReadMe
  search:
    terms: []
  styles:
    - brands
  unicode: f4d5
rebel:
  changes:
    - '4.1'
    - 5.0.0
  label: Rebel Alliance
  search:
    terms: []
  styles:
    - brands
  unicode: f1d0
receipt:
  changes:
    - 5.0.13
  label: Receipt
  search:
    terms:
      - check
      - invoice
      - table
  styles:
    - solid
  unicode: f543
recycle:
  changes:
    - '4.1'
    - 5.0.0
  label: Recycle
  search:
    terms: []
  styles:
    - solid
  unicode: f1b8
red-river:
  changes:
    - 5.0.0
  label: red river
  search:
    terms: []
  styles:
    - brands
  unicode: f3e3
reddit:
  changes:
    - '4.1'
    - 5.0.0
  label: reddit Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a1
reddit-alien:
  changes:
    - '4.5'
    - 5.0.0
  label: reddit Alien
  search:
    terms: []
  styles:
    - brands
  unicode: f281
reddit-square:
  changes:
    - '4.1'
    - 5.0.0
  label: reddit Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1a2
redo:
  changes:
    - '1'
    - 5.0.0
  label: Redo
  search:
    terms:
      - forward
      - refresh
      - reload
      - repeat
  styles:
    - solid
  unicode: f01e
redo-alt:
  changes:
    - 5.0.0
  label: Alternate Redo
  search:
    terms:
      - forward
      - refresh
      - reload
      - repeat
  styles:
    - solid
  unicode: f2f9
registered:
  changes:
    - '4.4'
    - 5.0.0
  label: Registered Trademark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25d
renren:
  changes:
    - '3.2'
    - 5.0.0
  label: Renren
  search:
    terms: []
  styles:
    - brands
  unicode: f18b
reply:
  changes:
    - '3'
    - 5.0.0
  label: Reply
  search:
    terms: []
  styles:
    - solid
  unicode: f3e5
reply-all:
  changes:
    - '3.1'
    - 5.0.0
  label: reply-all
  search:
    terms: []
  styles:
    - solid
  unicode: f122
replyd:
  changes:
    - 5.0.0
  label: replyd
  search:
    terms: []
  styles:
    - brands
  unicode: f3e6
republican:
  changes:
    - 5.5.0
  label: Republican
  search:
    terms:
      - american
      - conservative
      - election
      - elephant
      - politics
      - republican party
      - right
      - right-wing
      - usa
  styles:
    - solid
  unicode: f75e
researchgate:
  changes:
    - 5.0.11
  label: Researchgate
  search:
    terms: []
  styles:
    - brands
  unicode: f4f8
resolving:
  changes:
    - 5.0.0
  label: Resolving
  search:
    terms: []
  styles:
    - brands
  unicode: f3e7
retweet:
  changes:
    - '1'
    - 5.0.0
  label: Retweet
  search:
    terms:
      - refresh
      - reload
      - share
      - swap
  styles:
    - solid
  unicode: f079
rev:
  changes:
    - 5.1.0
    - 5.1.1
  label: Rev.io
  search:
    terms: []
  styles:
    - brands
  unicode: f5b2
ribbon:
  changes:
    - 5.0.9
  label: Ribbon
  search:
    terms:
      - badge
      - cause
      - lapel
      - pin
  styles:
    - solid
  unicode: f4d6
ring:
  changes:
    - 5.4.0
  label: Ring
  search:
    terms:
      - Dungeons & Dragons
      - Gollum
      - band
      - binding
      - d&d
      - dnd
      - fantasy
      - jewelry
      - precious
  styles:
    - solid
  unicode: f70b
road:
  changes:
    - '1'
    - 5.0.0
    - 5.2.0
  label: road
  search:
    terms:
      - street
  styles:
    - solid
  unicode: f018
robot:
  changes:
    - 5.0.13
  label: Robot
  search:
    terms: []
  styles:
    - solid
  unicode: f544
rocket:
  changes:
    - '3.1'
    - 5.0.0
  label: rocket
  search:
    terms:
      - app
  styles:
    - solid
  unicode: f135
rocketchat:
  changes:
    - 5.0.0
    - 5.4.2
  label: Rocket.Chat
  search:
    terms: []
  styles:
    - brands
  unicode: f3e8
rockrms:
  changes:
    - 5.0.0
  label: Rockrms
  search:
    terms: []
  styles:
    - brands
  unicode: f3e9
route:
  changes:
    - 5.0.9
  label: Route
  search:
    terms: []
  styles:
    - solid
  unicode: f4d7
rss:
  changes:
    - '2'
    - 5.0.0
  label: rss
  search:
    terms:
      - blog
  styles:
    - solid
  unicode: f09e
rss-square:
  changes:
    - '3.1'
    - 5.0.0
  label: RSS Square
  search:
    terms:
      - blog
      - feed
  styles:
    - solid
  unicode: f143
ruble-sign:
  changes:
    - '4'
    - 5.0.0
  label: Ruble Sign
  search:
    terms:
      - rub
  styles:
    - solid
  unicode: f158
ruler:
  changes:
    - 5.0.13
  label: Ruler
  search:
    terms: []
  styles:
    - solid
  unicode: f545
ruler-combined:
  changes:
    - 5.0.13
  label: Ruler Combined
  search:
    terms: []
  styles:
    - solid
  unicode: f546
ruler-horizontal:
  changes:
    - 5.0.13
  label: Ruler Horizontal
  search:
    terms: []
  styles:
    - solid
  unicode: f547
ruler-vertical:
  changes:
    - 5.0.13
  label: Ruler Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f548
running:
  changes:
    - 5.4.0
  label: Running
  search:
    terms:
      - jog
      - sprint
  styles:
    - solid
  unicode: f70c
rupee-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Indian Rupee Sign
  search:
    terms:
      - indian
      - inr
  styles:
    - solid
  unicode: f156
sad-cry:
  changes:
    - 5.1.0
  label: Crying Face
  search:
    terms:
      - emoticon
      - face
      - tear
      - tears
  styles:
    - solid
    - regular
  unicode: f5b3
sad-tear:
  changes:
    - 5.1.0
  label: Loudly Crying Face
  search:
    terms:
      - emoticon
      - face
      - tear
      - tears
  styles:
    - solid
    - regular
  unicode: f5b4
safari:
  changes:
    - '4.4'
    - 5.0.0
  label: Safari
  search:
    terms:
      - browser
  styles:
    - brands
  unicode: f267
sass:
  changes:
    - 5.0.0
  label: Sass
  search:
    terms: []
  styles:
    - brands
  unicode: f41e
save:
  changes:
    - '2'
    - 5.0.0
  label: Save
  search:
    terms:
      - floppy
      - floppy-o
  styles:
    - solid
    - regular
  unicode: f0c7
schlix:
  changes:
    - 5.0.0
  label: SCHLIX
  search:
    terms: []
  styles:
    - brands
  unicode: f3ea
school:
  changes:
    - 5.0.13
  label: School
  search:
    terms: []
  styles:
    - solid
  unicode: f549
screwdriver:
  changes:
    - 5.0.13
  label: Screwdriver
  search:
    terms:
      - admin
      - fix
      - repair
      - settings
      - tool
  styles:
    - solid
  unicode: f54a
scribd:
  changes:
    - '4.5'
    - 5.0.0
  label: Scribd
  search:
    terms: []
  styles:
    - brands
  unicode: f28a
scroll:
  changes:
    - 5.4.0
  label: Scroll
  search:
    terms:
      - Dungeons & Dragons
      - announcement
      - d&d
      - dnd
      - fantasy
      - paper
  styles:
    - solid
  unicode: f70e
search:
  changes:
    - '1'
    - 5.0.0
  label: Search
  search:
    terms:
      - bigger
      - enlarge
      - magnify
      - preview
      - zoom
  styles:
    - solid
  unicode: f002
search-dollar:
  changes:
    - 5.3.0
  label: Search Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f688
search-location:
  changes:
    - 5.3.0
  label: Search Location
  search:
    terms: []
  styles:
    - solid
  unicode: f689
search-minus:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: Search Minus
  search:
    terms:
      - minify
      - negative
      - smaller
      - zoom
      - zoom out
  styles:
    - solid
  unicode: f010
search-plus:
  changes:
    - '1'
    - 5.0.0
  label: Search Plus
  search:
    terms:
      - bigger
      - enlarge
      - magnify
      - positive
      - zoom
      - zoom in
  styles:
    - solid
  unicode: f00e
searchengin:
  changes:
    - 5.0.0
  label: Searchengin
  search:
    terms: []
  styles:
    - brands
  unicode: f3eb
seedling:
  changes:
    - 5.0.9
  label: Seedling
  search:
    terms: []
  styles:
    - solid
  unicode: f4d8
sellcast:
  changes:
    - 5.0.0
  label: Sellcast
  search:
    terms:
      - eercast
  styles:
    - brands
  unicode: f2da
sellsy:
  changes:
    - '4.3'
    - 5.0.0
  label: Sellsy
  search:
    terms: []
  styles:
    - brands
  unicode: f213
server:
  changes:
    - '4.3'
    - 5.0.0
  label: Server
  search:
    terms:
      - cpu
  styles:
    - solid
  unicode: f233
servicestack:
  changes:
    - 5.0.0
  label: Servicestack
  search:
    terms: []
  styles:
    - brands
  unicode: f3ec
shapes:
  changes:
    - 5.2.0
  label: Shapes
  search:
    terms:
      - circle
      - square
      - triangle
  styles:
    - solid
  unicode: f61f
share:
  changes:
    - '1'
    - 5.0.0
  label: Share
  search:
    terms: []
  styles:
    - solid
  unicode: f064
share-alt:
  changes:
    - '4.1'
    - 5.0.0
  label: Alternate Share
  search:
    terms: []
  styles:
    - solid
  unicode: f1e0
share-alt-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Alternate Share Square
  search:
    terms: []
  styles:
    - solid
  unicode: f1e1
share-square:
  changes:
    - '3.1'
    - 5.0.0
  label: Share Square
  search:
    terms:
      - send
      - social
  styles:
    - solid
    - regular
  unicode: f14d
shekel-sign:
  changes:
    - '4.2'
    - 5.0.0
  label: Shekel Sign
  search:
    terms:
      - ils
  styles:
    - solid
  unicode: f20b
shield-alt:
  changes:
    - 5.0.0
  label: Alternate Shield
  search:
    terms:
      - achievement
      - award
      - block
      - defend
      - security
      - winner
  styles:
    - solid
  unicode: f3ed
ship:
  changes:
    - '4.3'
    - 5.0.0
  label: Ship
  search:
    terms:
      - boat
      - sea
  styles:
    - solid
  unicode: f21a
shipping-fast:
  changes:
    - 5.0.7
  label: Shipping Fast
  search:
    terms: []
  styles:
    - solid
  unicode: f48b
shirtsinbulk:
  changes:
    - '4.3'
    - 5.0.0
  label: Shirts in Bulk
  search:
    terms: []
  styles:
    - brands
  unicode: f214
shoe-prints:
  changes:
    - 5.0.13
  label: Shoe Prints
  search:
    terms:
      - feet
      - footprints
      - steps
  styles:
    - solid
  unicode: f54b
shopping-bag:
  changes:
    - '4.5'
    - 5.0.0
  label: Shopping Bag
  search:
    terms: []
  styles:
    - solid
  unicode: f290
shopping-basket:
  changes:
    - '4.5'
    - 5.0.0
  label: Shopping Basket
  search:
    terms: []
  styles:
    - solid
  unicode: f291
shopping-cart:
  changes:
    - '1'
    - 5.0.0
  label: shopping-cart
  search:
    terms:
      - buy
      - checkout
      - payment
      - purchase
  styles:
    - solid
  unicode: f07a
shopware:
  changes:
    - 5.1.0
  label: Shopware
  search:
    terms: []
  styles:
    - brands
  unicode: f5b5
shower:
  changes:
    - '4.7'
    - 5.0.0
  label: Shower
  search:
    terms: []
  styles:
    - solid
  unicode: f2cc
shuttle-van:
  changes:
    - 5.1.0
  label: Shuttle Van
  search:
    terms:
      - machine
      - public-transportation
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f5b6
sign:
  changes:
    - 5.0.9
  label: Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f4d9
sign-in-alt:
  changes:
    - 5.0.0
  label: Alternate Sign In
  search:
    terms:
      - arrow
      - enter
      - join
      - log in
      - login
      - sign in
      - sign up
      - sign-in
      - signin
      - signup
  styles:
    - solid
  unicode: f2f6
sign-language:
  changes:
    - '4.6'
    - 5.0.0
  label: Sign Language
  search:
    terms: []
  styles:
    - solid
  unicode: f2a7
sign-out-alt:
  changes:
    - 5.0.0
  label: Alternate Sign Out
  search:
    terms:
      - arrow
      - exit
      - leave
      - log out
      - logout
      - sign-out
  styles:
    - solid
  unicode: f2f5
signal:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: signal
  search:
    terms:
      - bars
      - graph
      - online
      - status
  styles:
    - solid
  unicode: f012
signature:
  changes:
    - 5.1.0
  label: Signature
  search:
    terms:
      - John Hancock
      - cursive
      - name
      - writing
  styles:
    - solid
  unicode: f5b7
simplybuilt:
  changes:
    - '4.3'
    - 5.0.0
  label: SimplyBuilt
  search:
    terms: []
  styles:
    - brands
  unicode: f215
sistrix:
  changes:
    - 5.0.0
  label: SISTRIX
  search:
    terms: []
  styles:
    - brands
  unicode: f3ee
sitemap:
  changes:
    - '2'
    - 5.0.0
    - 5.0.13
  label: Sitemap
  search:
    terms:
      - directory
      - hierarchy
      - ia
      - information architecture
      - organization
  styles:
    - solid
  unicode: f0e8
sith:
  changes:
    - 5.0.12
  label: Sith
  search:
    terms: []
  styles:
    - brands
  unicode: f512
skull:
  changes:
    - 5.0.13
  label: Skull
  search:
    terms:
      - bones
      - skeleton
      - yorick
  styles:
    - solid
  unicode: f54c
skull-crossbones:
  changes:
    - 5.4.0
  label: Skull & Crossbones
  search:
    terms:
      - Dungeons & Dragons
      - alert
      - bones
      - d&d
      - danger
      - dead
      - deadly
      - death
      - dnd
      - fantasy
      - halloween
      - holiday
      - jolly-roger
      - pirate
      - poison
      - skeleton
      - warning
  styles:
    - solid
  unicode: f714
skyatlas:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
  label: skyatlas
  search:
    terms: []
  styles:
    - brands
  unicode: f216
skype:
  changes:
    - '3.2'
    - 5.0.0
  label: Skype
  search:
    terms: []
  styles:
    - brands
  unicode: f17e
slack:
  changes:
    - '4.1'
    - 5.0.0
  label: Slack Logo
  search:
    terms:
      - anchor
      - hash
      - hashtag
  styles:
    - brands
  unicode: f198
slack-hash:
  changes:
    - 5.0.0
  label: Slack Hashtag
  search:
    terms:
      - anchor
      - hash
      - hashtag
  styles:
    - brands
  unicode: f3ef
slash:
  changes:
    - 5.4.0
  label: Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f715
sliders-h:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.11
  label: Horizontal Sliders
  search:
    terms:
      - settings
      - sliders
  styles:
    - solid
  unicode: f1de
slideshare:
  changes:
    - '4.2'
    - 5.0.0
  label: Slideshare
  search:
    terms: []
  styles:
    - brands
  unicode: f1e7
smile:
  changes:
    - '3.1'
    - 5.0.0
    - 5.0.9
    - 5.1.0
  label: Smiling Face
  search:
    terms:
      - approve
      - emoticon
      - face
      - happy
      - rating
      - satisfied
  styles:
    - solid
    - regular
  unicode: f118
smile-beam:
  changes:
    - 5.1.0
  label: Beaming Face With Smiling Eyes
  search:
    terms:
      - emoticon
      - face
      - happy
      - positive
  styles:
    - solid
    - regular
  unicode: f5b8
smile-wink:
  changes:
    - 5.1.0
  label: Winking Face
  search:
    terms:
      - emoticon
      - face
      - happy
  styles:
    - solid
    - regular
  unicode: f4da
smog:
  changes:
    - 5.5.0
  label: Smog
  search:
    terms:
      - dragon
  styles:
    - solid
  unicode: f75f
smoking:
  changes:
    - 5.0.7
  label: Smoking
  search:
    terms:
      - cigarette
      - nicotine
      - smoking status
  styles:
    - solid
  unicode: f48d
smoking-ban:
  changes:
    - 5.0.13
  label: Smoking Ban
  search:
    terms:
      - no smoking
      - non-smoking
  styles:
    - solid
  unicode: f54d
snapchat:
  changes:
    - '4.6'
    - 5.0.0
  label: Snapchat
  search:
    terms: []
  styles:
    - brands
  unicode: f2ab
snapchat-ghost:
  changes:
    - '4.6'
    - 5.0.0
  label: Snapchat Ghost
  search:
    terms: []
  styles:
    - brands
  unicode: f2ac
snapchat-square:
  changes:
    - '4.6'
    - 5.0.0
  label: Snapchat Square
  search:
    terms: []
  styles:
    - brands
  unicode: f2ad
snowflake:
  changes:
    - '4.7'
    - 5.0.0
    - 5.5.0
  label: Snowflake
  search:
    terms:
      - precipitation
      - seasonal
      - winter
  styles:
    - solid
    - regular
  unicode: f2dc
socks:
  changes:
    - 5.3.0
  label: Socks
  search:
    terms:
      - business socks
      - business time
      - flight of the conchords
      - wednesday
  styles:
    - solid
  unicode: f696
solar-panel:
  changes:
    - 5.1.0
  label: Solar Panel
  search:
    terms:
      - clean
      - eco-friendly
      - energy
      - green
      - sun
  styles:
    - solid
  unicode: f5ba
sort:
  changes:
    - '2'
    - 5.0.0
  label: Sort
  search:
    terms:
      - order
  styles:
    - solid
  unicode: f0dc
sort-alpha-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Alpha Down
  search:
    terms:
      - sort-alpha-asc
  styles:
    - solid
  unicode: f15d
sort-alpha-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Alpha Up
  search:
    terms:
      - sort-alpha-desc
  styles:
    - solid
  unicode: f15e
sort-amount-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Amount Down
  search:
    terms:
      - sort-amount-asc
  styles:
    - solid
  unicode: f160
sort-amount-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Amount Up
  search:
    terms:
      - sort-amount-desc
  styles:
    - solid
  unicode: f161
sort-down:
  changes:
    - '2'
    - 5.0.0
  label: Sort Down (Descending)
  search:
    terms:
      - arrow
      - descending
      - sort-desc
  styles:
    - solid
  unicode: f0dd
sort-numeric-down:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Numeric Down
  search:
    terms:
      - numbers
      - sort-numeric-asc
  styles:
    - solid
  unicode: f162
sort-numeric-up:
  changes:
    - '3.2'
    - 5.0.0
  label: Sort Numeric Up
  search:
    terms:
      - numbers
      - sort-numeric-desc
  styles:
    - solid
  unicode: f163
sort-up:
  changes:
    - '2'
    - 5.0.0
  label: Sort Up (Ascending)
  search:
    terms:
      - arrow
      - ascending
      - sort-asc
  styles:
    - solid
  unicode: f0de
soundcloud:
  changes:
    - '4.1'
    - 5.0.0
  label: SoundCloud
  search:
    terms: []
  styles:
    - brands
  unicode: f1be
spa:
  changes:
    - 5.1.0
  label: Spa
  search:
    terms:
      - flora
      - mindfullness
      - plant
      - wellness
  styles:
    - solid
  unicode: f5bb
space-shuttle:
  changes:
    - '4.1'
    - 5.0.0
  label: Space Shuttle
  search:
    terms:
      - astronaut
      - machine
      - nasa
      - rocket
      - transportation
  styles:
    - solid
  unicode: f197
speakap:
  changes:
    - 5.0.0
    - 5.4.0
  label: Speakap
  search:
    terms: []
  styles:
    - brands
  unicode: f3f3
spider:
  changes:
    - 5.4.0
  label: Spider
  search:
    terms:
      - arachnid
      - bug
      - charlotte
      - crawl
      - eight
      - halloween
      - holiday
  styles:
    - solid
  unicode: f717
spinner:
  changes:
    - '3'
    - 5.0.0
  label: Spinner
  search:
    terms:
      - loading
      - progress
  styles:
    - solid
  unicode: f110
splotch:
  changes:
    - 5.1.0
  label: Splotch
  search:
    terms: []
  styles:
    - solid
  unicode: f5bc
spotify:
  changes:
    - '4.1'
    - 5.0.0
  label: Spotify
  search:
    terms: []
  styles:
    - brands
  unicode: f1bc
spray-can:
  changes:
    - 5.1.0
  label: Spray Can
  search:
    terms: []
  styles:
    - solid
  unicode: f5bd
square:
  changes:
    - '2'
    - 5.0.0
  label: Square
  search:
    terms:
      - block
      - box
  styles:
    - solid
    - regular
  unicode: f0c8
square-full:
  changes:
    - 5.0.5
  label: Square Full
  search:
    terms: []
  styles:
    - solid
  unicode: f45c
square-root-alt:
  changes:
    - 5.3.0
  label: Square Root Alternate
  search:
    terms: []
  styles:
    - solid
  unicode: f698
squarespace:
  changes:
    - 5.1.0
  label: Squarespace
  search:
    terms: []
  styles:
    - brands
  unicode: f5be
stack-exchange:
  changes:
    - '4'
    - 5.0.0
    - 5.0.3
  label: Stack Exchange
  search:
    terms: []
  styles:
    - brands
  unicode: f18d
stack-overflow:
  changes:
    - '3.2'
    - 5.0.0
  label: Stack Overflow
  search:
    terms: []
  styles:
    - brands
  unicode: f16c
stamp:
  changes:
    - 5.1.0
  label: Stamp
  search:
    terms: []
  styles:
    - solid
  unicode: f5bf
star:
  changes:
    - '1'
    - 5.0.0
  label: Star
  search:
    terms:
      - achievement
      - award
      - favorite
      - important
      - night
      - rating
      - score
  styles:
    - solid
    - regular
  unicode: f005
star-and-crescent:
  changes:
    - 5.3.0
  label: Star and Crescent
  search:
    terms:
      - islam
      - muslim
  styles:
    - solid
  unicode: f699
star-half:
  changes:
    - '1'
    - 5.0.0
  label: star-half
  search:
    terms:
      - achievement
      - award
      - rating
      - score
      - star-half-empty
      - star-half-full
  styles:
    - solid
    - regular
  unicode: f089
star-half-alt:
  changes:
    - 5.1.0
  label: Alternate Star Half
  search:
    terms:
      - achievement
      - award
      - rating
      - score
      - star-half-empty
      - star-half-full
  styles:
    - solid
  unicode: f5c0
star-of-david:
  changes:
    - 5.3.0
  label: Star of David
  search:
    terms:
      - jewish
      - judaism
  styles:
    - solid
  unicode: f69a
star-of-life:
  changes:
    - 5.2.0
  label: Star of Life
  search:
    terms: []
  styles:
    - solid
  unicode: f621
staylinked:
  changes:
    - 5.0.0
  label: StayLinked
  search:
    terms: []
  styles:
    - brands
  unicode: f3f5
steam:
  changes:
    - '4.1'
    - 5.0.0
  label: Steam
  search:
    terms: []
  styles:
    - brands
  unicode: f1b6
steam-square:
  changes:
    - '4.1'
    - 5.0.0
  label: Steam Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1b7
steam-symbol:
  changes:
    - 5.0.0
  label: Steam Symbol
  search:
    terms: []
  styles:
    - brands
  unicode: f3f6
step-backward:
  changes:
    - '1'
    - 5.0.0
  label: step-backward
  search:
    terms:
      - beginning
      - first
      - previous
      - rewind
      - start
  styles:
    - solid
  unicode: f048
step-forward:
  changes:
    - '1'
    - 5.0.0
  label: step-forward
  search:
    terms:
      - end
      - last
      - next
  styles:
    - solid
  unicode: f051
stethoscope:
  changes:
    - '3'
    - 5.0.0
    - 5.0.7
  label: Stethoscope
  search:
    terms: []
  styles:
    - solid
  unicode: f0f1
sticker-mule:
  changes:
    - 5.0.0
  label: Sticker Mule
  search:
    terms: []
  styles:
    - brands
  unicode: f3f7
sticky-note:
  changes:
    - '4.4'
    - 5.0.0
  label: Sticky Note
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f249
stop:
  changes:
    - '1'
    - 5.0.0
  label: stop
  search:
    terms:
      - block
      - box
      - square
  styles:
    - solid
  unicode: f04d
stop-circle:
  changes:
    - '4.5'
    - 5.0.0
  label: Stop Circle
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f28d
stopwatch:
  changes:
    - 5.0.0
  label: Stopwatch
  search:
    terms:
      - time
  styles:
    - solid
  unicode: f2f2
store:
  changes:
    - 5.0.13
  label: Store
  search:
    terms: []
  styles:
    - solid
  unicode: f54e
store-alt:
  changes:
    - 5.0.13
  label: Alternate Store
  search:
    terms: []
  styles:
    - solid
  unicode: f54f
strava:
  changes:
    - 5.0.0
    - 5.0.1
  label: Strava
  search:
    terms: []
  styles:
    - brands
  unicode: f428
stream:
  changes:
    - 5.0.13
  label: Stream
  search:
    terms: []
  styles:
    - solid
  unicode: f550
street-view:
  changes:
    - '4.3'
    - 5.0.0
    - 5.2.0
  label: Street View
  search:
    terms:
      - map
  styles:
    - solid
  unicode: f21d
strikethrough:
  changes:
    - '2'
    - 5.0.0
  label: Strikethrough
  search:
    terms: []
  styles:
    - solid
  unicode: f0cc
stripe:
  changes:
    - 5.0.0
    - 5.0.3
  label: Stripe
  search:
    terms: []
  styles:
    - brands
  unicode: f429
stripe-s:
  changes:
    - 5.0.1
  label: Stripe S
  search:
    terms: []
  styles:
    - brands
  unicode: f42a
stroopwafel:
  changes:
    - 5.0.13
  label: Stroopwafel
  search:
    terms:
      - dessert
      - food
      - sweets
      - waffle
  styles:
    - solid
  unicode: f551
studiovinari:
  changes:
    - 5.0.0
  label: Studio Vinari
  search:
    terms: []
  styles:
    - brands
  unicode: f3f8
stumbleupon:
  changes:
    - '4.1'
    - 5.0.0
  label: StumbleUpon Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a4
stumbleupon-circle:
  changes:
    - '4.1'
    - 5.0.0
  label: StumbleUpon Circle
  search:
    terms: []
  styles:
    - brands
  unicode: f1a3
subscript:
  changes:
    - '3.1'
    - 5.0.0
  label: subscript
  search:
    terms: []
  styles:
    - solid
  unicode: f12c
subway:
  changes:
    - '4.3'
    - 5.0.0
  label: Subway
  search:
    terms:
      - machine
      - railway
      - train
      - transportation
      - vehicle
  styles:
    - solid
  unicode: f239
suitcase:
  changes:
    - '3'
    - 5.0.0
    - 5.0.9
  label: Suitcase
  search:
    terms:
      - baggage
      - luggage
      - move
      - suitcase
      - travel
      - trip
  styles:
    - solid
  unicode: f0f2
suitcase-rolling:
  changes:
    - 5.1.0
  label: Suitcase Rolling
  search:
    terms: []
  styles:
    - solid
  unicode: f5c1
sun:
  changes:
    - '3.2'
    - 5.0.0
    - 5.5.0
  label: Sun
  search:
    terms:
      - brighten
      - contrast
      - day
      - lighter
      - sol
      - solar
      - star
      - weather
  styles:
    - solid
    - regular
  unicode: f185
superpowers:
  changes:
    - '4.7'
    - 5.0.0
  label: Superpowers
  search:
    terms: []
  styles:
    - brands
  unicode: f2dd
superscript:
  changes:
    - '3.1'
    - 5.0.0
  label: superscript
  search:
    terms:
      - exponential
  styles:
    - solid
  unicode: f12b
supple:
  changes:
    - 5.0.0
  label: Supple
  search:
    terms: []
  styles:
    - brands
  unicode: f3f9
surprise:
  changes:
    - 5.1.0
  label: Hushed Face
  search:
    terms:
      - emoticon
      - face
      - shocked
  styles:
    - solid
    - regular
  unicode: f5c2
swatchbook:
  changes:
    - 5.1.0
  label: Swatchbook
  search:
    terms: []
  styles:
    - solid
  unicode: f5c3
swimmer:
  changes:
    - 5.1.0
  label: Swimmer
  search:
    terms:
      - athlete
      - head
      - man
      - person
      - water
  styles:
    - solid
  unicode: f5c4
swimming-pool:
  changes:
    - 5.1.0
  label: Swimming Pool
  search:
    terms:
      - ladder
      - recreation
      - water
  styles:
    - solid
  unicode: f5c5
synagogue:
  changes:
    - 5.3.0
  label: Synagogue
  search:
    terms:
      - building
      - jewish
      - judaism
      - star of david
      - temple
  styles:
    - solid
  unicode: f69b
sync:
  changes:
    - '1'
    - 5.0.0
  label: Sync
  search:
    terms:
      - exchange
      - refresh
      - reload
      - rotate
      - swap
  styles:
    - solid
  unicode: f021
sync-alt:
  changes:
    - 5.0.0
  label: Alternate Sync
  search:
    terms:
      - refresh
      - reload
      - rotate
  styles:
    - solid
  unicode: f2f1
syringe:
  changes:
    - 5.0.7
  label: Syringe
  search:
    terms:
      - immunizations
      - needle
  styles:
    - solid
  unicode: f48e
table:
  changes:
    - '2'
    - 5.0.0
  label: table
  search:
    terms:
      - data
      - excel
      - spreadsheet
  styles:
    - solid
  unicode: f0ce
table-tennis:
  changes:
    - 5.0.5
  label: Table Tennis
  search:
    terms: []
  styles:
    - solid
  unicode: f45d
tablet:
  changes:
    - '3'
    - 5.0.0
  label: tablet
  search:
    terms:
      - apple
      - device
      - ipad
      - kindle
      - screen
  styles:
    - solid
  unicode: f10a
tablet-alt:
  changes:
    - 5.0.0
  label: Alternate Tablet
  search:
    terms:
      - apple
      - device
      - ipad
      - kindle
      - screen
  styles:
    - solid
  unicode: f3fa
tablets:
  changes:
    - 5.0.7
  label: Tablets
  search:
    terms:
      - drugs
      - medicine
  styles:
    - solid
  unicode: f490
tachometer-alt:
  changes:
    - 5.0.0
    - 5.2.0
  label: Alternate Tachometer
  search:
    terms:
      - dashboard
      - tachometer
  styles:
    - solid
  unicode: f3fd
tag:
  changes:
    - '1'
    - 5.0.0
  label: tag
  search:
    terms:
      - label
  styles:
    - solid
  unicode: f02b
tags:
  changes:
    - '1'
    - 5.0.0
  label: tags
  search:
    terms:
      - labels
  styles:
    - solid
  unicode: f02c
tape:
  changes:
    - 5.0.9
  label: Tape
  search:
    terms: []
  styles:
    - solid
  unicode: f4db
tasks:
  changes:
    - '2'
    - 5.0.0
  label: Tasks
  search:
    terms:
      - downloading
      - downloads
      - loading
      - progress
      - settings
  styles:
    - solid
  unicode: f0ae
taxi:
  changes:
    - '4.1'
    - 5.0.0
    - 5.1.0
  label: Taxi
  search:
    terms:
      - cab
      - cabbie
      - car
      - car service
      - lyft
      - machine
      - transportation
      - uber
      - vehicle
  styles:
    - solid
  unicode: f1ba
teamspeak:
  changes:
    - 5.0.11
    - 5.1.0
  label: TeamSpeak
  search:
    terms: []
  styles:
    - brands
  unicode: f4f9
teeth:
  changes:
    - 5.2.0
  label: Teeth
  search:
    terms: []
  styles:
    - solid
  unicode: f62e
teeth-open:
  changes:
    - 5.2.0
  label: Teeth Open
  search:
    terms: []
  styles:
    - solid
  unicode: f62f
telegram:
  changes:
    - '4.7'
    - 5.0.0
  label: Telegram
  search:
    terms: []
  styles:
    - brands
  unicode: f2c6
telegram-plane:
  changes:
    - 5.0.0
  label: Telegram Plane
  search:
    terms: []
  styles:
    - brands
  unicode: f3fe
temperature-high:
  changes:
    - 5.5.0
  label: High Temperature
  search:
    terms:
      - mercury
      - thermometer
      - warm
  styles:
    - solid
  unicode: f769
temperature-low:
  changes:
    - 5.5.0
  label: Low Temperature
  search:
    terms:
      - cool
      - mercury
      - thermometer
  styles:
    - solid
  unicode: f76b
tencent-weibo:
  changes:
    - '4.1'
    - 5.0.0
  label: Tencent Weibo
  search:
    terms: []
  styles:
    - brands
  unicode: f1d5
terminal:
  changes:
    - '3.1'
    - 5.0.0
  label: Terminal
  search:
    terms:
      - code
      - command
      - console
      - prompt
  styles:
    - solid
  unicode: f120
text-height:
  changes:
    - '1'
    - 5.0.0
  label: text-height
  search:
    terms: []
  styles:
    - solid
  unicode: f034
text-width:
  changes:
    - '1'
    - 5.0.0
  label: text-width
  search:
    terms: []
  styles:
    - solid
  unicode: f035
th:
  changes:
    - '1'
    - 5.0.0
  label: th
  search:
    terms:
      - blocks
      - boxes
      - grid
      - squares
  styles:
    - solid
  unicode: f00a
th-large:
  changes:
    - '1'
    - 5.0.0
  label: th-large
  search:
    terms:
      - blocks
      - boxes
      - grid
      - squares
  styles:
    - solid
  unicode: f009
th-list:
  changes:
    - '1'
    - 5.0.0
  label: th-list
  search:
    terms:
      - checklist
      - completed
      - done
      - finished
      - ol
      - todo
      - ul
  styles:
    - solid
  unicode: f00b
the-red-yeti:
  changes:
    - 5.3.0
  label: The Red Yeti
  search:
    terms: []
  styles:
    - brands
  unicode: f69d
theater-masks:
  changes:
    - 5.2.0
  label: Theater Masks
  search:
    terms: []
  styles:
    - solid
  unicode: f630
themeco:
  changes:
    - 5.1.0
  label: Themeco
  search:
    terms: []
  styles:
    - brands
  unicode: f5c6
themeisle:
  changes:
    - '4.6'
    - 5.0.0
  label: ThemeIsle
  search:
    terms: []
  styles:
    - brands
  unicode: f2b2
thermometer:
  changes:
    - 5.0.7
  label: Thermometer
  search:
    terms:
      - mercury
      - status
      - temperature
  styles:
    - solid
  unicode: f491
thermometer-empty:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer Empty
  search:
    terms:
      - mercury
      - status
      - temperature
  styles:
    - solid
  unicode: f2cb
thermometer-full:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer Full
  search:
    terms:
      - fever
      - mercury
      - status
      - temperature
  styles:
    - solid
  unicode: f2c7
thermometer-half:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer 1/2 Full
  search:
    terms:
      - mercury
      - status
      - temperature
  styles:
    - solid
  unicode: f2c9
thermometer-quarter:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer 1/4 Full
  search:
    terms:
      - mercury
      - status
      - temperature
  styles:
    - solid
  unicode: f2ca
thermometer-three-quarters:
  changes:
    - '4.7'
    - 5.0.0
  label: Thermometer 3/4 Full
  search:
    terms:
      - mercury
      - status
      - temperature
  styles:
    - solid
  unicode: f2c8
think-peaks:
  changes:
    - 5.4.2
  label: Think Peaks
  search:
    terms: []
  styles:
    - brands
  unicode: f731
thumbs-down:
  changes:
    - '3.2'
    - 5.0.0
  label: thumbs-down
  search:
    terms:
      - disagree
      - disapprove
      - dislike
      - hand
      - thumbs-o-down
  styles:
    - solid
    - regular
  unicode: f165
thumbs-up:
  changes:
    - '3.2'
    - 5.0.0
  label: thumbs-up
  search:
    terms:
      - agree
      - approve
      - favorite
      - hand
      - like
      - ok
      - okay
      - success
      - thumbs-o-up
      - 'yes'
      - you got it dude
  styles:
    - solid
    - regular
  unicode: f164
thumbtack:
  changes:
    - '1'
    - 5.0.0
  label: Thumbtack
  search:
    terms:
      - coordinates
      - location
      - marker
      - pin
      - thumb-tack
  styles:
    - solid
  unicode: f08d
ticket-alt:
  changes:
    - 5.0.0
  label: Alternate Ticket
  search:
    terms:
      - ticket
  styles:
    - solid
  unicode: f3ff
times:
  changes:
    - '1'
    - 5.0.0
    - 5.0.13
  label: Times
  search:
    terms:
      - close
      - cross
      - error
      - exit
      - incorrect
      - notice
      - notification
      - notify
      - problem
      - wrong
      - x
  styles:
    - solid
  unicode: f00d
times-circle:
  changes:
    - '1'
    - 5.0.0
  label: Times Circle
  search:
    terms:
      - close
      - cross
      - exit
      - incorrect
      - notice
      - notification
      - notify
      - problem
      - wrong
      - x
  styles:
    - solid
    - regular
  unicode: f057
tint:
  changes:
    - '1'
    - 5.0.0
    - 5.1.0
  label: tint
  search:
    terms:
      - drop
      - droplet
      - raindrop
      - waterdrop
  styles:
    - solid
  unicode: f043
tint-slash:
  changes:
    - 5.1.0
  label: Tint Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f5c7
tired:
  changes:
    - 5.1.0
  label: Tired Face
  search:
    terms:
      - emoticon
      - face
      - grumpy
  styles:
    - solid
    - regular
  unicode: f5c8
toggle-off:
  changes:
    - '4.2'
    - 5.0.0
  label: Toggle Off
  search:
    terms:
      - switch
  styles:
    - solid
  unicode: f204
toggle-on:
  changes:
    - '4.2'
    - 5.0.0
  label: Toggle On
  search:
    terms:
      - switch
  styles:
    - solid
  unicode: f205
toilet-paper:
  changes:
    - 5.4.0
  label: Toilet Paper
  search:
    terms:
      - bathroom
      - halloween
      - holiday
      - lavatory
      - prank
      - restroom
      - roll
  styles:
    - solid
  unicode: f71e
toolbox:
  changes:
    - 5.0.13
  label: Toolbox
  search:
    terms:
      - admin
      - container
      - fix
      - repair
      - settings
      - tools
  styles:
    - solid
  unicode: f552
tooth:
  changes:
    - 5.1.0
  label: Tooth
  search:
    terms:
      - bicuspid
      - dental
      - molar
      - mouth
      - teeth
  styles:
    - solid
  unicode: f5c9
torah:
  changes:
    - 5.3.0
  label: Torah
  search:
    terms:
      - book
      - jewish
      - judaism
  styles:
    - solid
  unicode: f6a0
torii-gate:
  changes:
    - 5.3.0
  label: Torii Gate
  search:
    terms:
      - building
      - shintoism
  styles:
    - solid
  unicode: f6a1
tractor:
  changes:
    - 5.4.0
  label: Tractor
  search:
    terms: []
  styles:
    - solid
  unicode: f722
trade-federation:
  changes:
    - 5.0.12
  label: Trade Federation
  search:
    terms: []
  styles:
    - brands
  unicode: f513
trademark:
  changes:
    - '4.4'
    - 5.0.0
  label: Trademark
  search:
    terms: []
  styles:
    - solid
  unicode: f25c
traffic-light:
  changes:
    - 5.2.0
  label: Traffic Light
  search:
    terms: []
  styles:
    - solid
  unicode: f637
train:
  changes:
    - '4.3'
    - 5.0.0
  label: Train
  search:
    terms:
      - bullet
      - locomotive
      - railway
  styles:
    - solid
  unicode: f238
transgender:
  changes:
    - '4.3'
    - 5.0.0
  label: Transgender
  search:
    terms:
      - intersex
  styles:
    - solid
  unicode: f224
transgender-alt:
  changes:
    - '4.3'
    - 5.0.0
  label: Alternate Transgender
  search:
    terms: []
  styles:
    - solid
  unicode: f225
trash:
  changes:
    - '4.2'
    - 5.0.0
  label: Trash
  search:
    terms:
      - delete
      - garbage
      - hide
      - remove
  styles:
    - solid
  unicode: f1f8
trash-alt:
  changes:
    - 5.0.0
  label: Alternate Trash
  search:
    terms:
      - delete
      - garbage
      - hide
      - remove
      - trash
      - trash-o
  styles:
    - solid
    - regular
  unicode: f2ed
tree:
  changes:
    - '4.1'
    - 5.0.0
  label: Tree
  search:
    terms:
      - bark
      - fall
      - flora
      - forest
      - nature
      - plant
      - seasonal
  styles:
    - solid
  unicode: f1bb
trello:
  changes:
    - '3.2'
    - 5.0.0
  label: Trello
  search:
    terms: []
  styles:
    - brands
  unicode: f181
tripadvisor:
  changes:
    - '4.4'
    - 5.0.0
  label: TripAdvisor
  search:
    terms: []
  styles:
    - brands
  unicode: f262
trophy:
  changes:
    - '1'
    - 5.0.0
  label: trophy
  search:
    terms:
      - achievement
      - award
      - cup
      - game
      - winner
  styles:
    - solid
  unicode: f091
truck:
  changes:
    - '2'
    - 5.0.0
    - 5.0.7
  label: truck
  search:
    terms:
      - delivery
      - shipping
  styles:
    - solid
  unicode: f0d1
truck-loading:
  changes:
    - 5.0.9
  label: Truck Loading
  search:
    terms: []
  styles:
    - solid
  unicode: f4de
truck-monster:
  changes:
    - 5.2.0
  label: Truck Monster
  search:
    terms: []
  styles:
    - solid
  unicode: f63b
truck-moving:
  changes:
    - 5.0.9
  label: Truck Moving
  search:
    terms: []
  styles:
    - solid
  unicode: f4df
truck-pickup:
  changes:
    - 5.2.0
  label: Truck Side
  search:
    terms: []
  styles:
    - solid
  unicode: f63c
tshirt:
  changes:
    - 5.0.13
  label: T-Shirt
  search:
    terms:
      - cloth
      - clothing
  styles:
    - solid
  unicode: f553
tty:
  changes:
    - '4.2'
    - 5.0.0
  label: TTY
  search:
    terms: []
  styles:
    - solid
  unicode: f1e4
tumblr:
  changes:
    - '3.2'
    - 5.0.0
  label: Tumblr
  search:
    terms: []
  styles:
    - brands
  unicode: f173
tumblr-square:
  changes:
    - '3.2'
    - 5.0.0
  label: Tumblr Square
  search:
    terms: []
  styles:
    - brands
  unicode: f174
tv:
  changes:
    - '4.4'
    - 5.0.0
  label: Television
  search:
    terms:
      - computer
      - display
      - monitor
      - television
  styles:
    - solid
  unicode: f26c
twitch:
  changes:
    - '4.2'
    - 5.0.0
  label: Twitch
  search:
    terms: []
  styles:
    - brands
  unicode: f1e8
twitter:
  changes:
    - '2'
    - 5.0.0
  label: Twitter
  search:
    terms:
      - social network
      - tweet
  styles:
    - brands
  unicode: f099
twitter-square:
  changes:
    - '1'
    - 5.0.0
  label: Twitter Square
  search:
    terms:
      - social network
      - tweet
  styles:
    - brands
  unicode: f081
typo3:
  changes:
    - 5.0.1
  label: Typo3
  search:
    terms: []
  styles:
    - brands
  unicode: f42b
uber:
  changes:
    - 5.0.0
  label: Uber
  search:
    terms: []
  styles:
    - brands
  unicode: f402
uikit:
  changes:
    - 5.0.0
  label: UIkit
  search:
    terms: []
  styles:
    - brands
  unicode: f403
umbrella:
  changes:
    - '2'
    - 5.0.0
  label: Umbrella
  search:
    terms:
      - protection
      - rain
  styles:
    - solid
  unicode: f0e9
umbrella-beach:
  changes:
    - 5.1.0
  label: Umbrella Beach
  search:
    terms:
      - protection
      - recreation
      - sun
  styles:
    - solid
  unicode: f5ca
underline:
  changes:
    - '2'
    - 5.0.0
  label: Underline
  search:
    terms: []
  styles:
    - solid
  unicode: f0cd
undo:
  changes:
    - '2'
    - 5.0.0
  label: Undo
  search:
    terms:
      - back
      - control z
      - exchange
      - oops
      - return
      - rotate
      - swap
  styles:
    - solid
  unicode: f0e2
undo-alt:
  changes:
    - 5.0.0
  label: Alternate Undo
  search:
    terms:
      - back
      - control z
      - exchange
      - oops
      - return
      - swap
  styles:
    - solid
  unicode: f2ea
uniregistry:
  changes:
    - 5.0.0
  label: Uniregistry
  search:
    terms: []
  styles:
    - brands
  unicode: f404
universal-access:
  changes:
    - '4.6'
    - 5.0.0
  label: Universal Access
  search:
    terms: []
  styles:
    - solid
  unicode: f29a
university:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: University
  search:
    terms:
      - bank
      - institution
  styles:
    - solid
  unicode: f19c
unlink:
  changes:
    - '3.1'
    - 5.0.0
  label: unlink
  search:
    terms:
      - chain
      - chain-broken
      - remove
  styles:
    - solid
  unicode: f127
unlock:
  changes:
    - '2'
    - 5.0.0
  label: unlock
  search:
    terms:
      - admin
      - lock
      - password
      - protect
  styles:
    - solid
  unicode: f09c
unlock-alt:
  changes:
    - '3.1'
    - 5.0.0
  label: Alternate Unlock
  search:
    terms:
      - admin
      - lock
      - password
      - protect
  styles:
    - solid
  unicode: f13e
untappd:
  changes:
    - 5.0.0
  label: Untappd
  search:
    terms: []
  styles:
    - brands
  unicode: f405
upload:
  changes:
    - '1'
    - 5.0.0
  label: Upload
  search:
    terms:
      - export
      - publish
  styles:
    - solid
  unicode: f093
usb:
  changes:
    - '4.5'
    - 5.0.0
  label: USB
  search:
    terms: []
  styles:
    - brands
  unicode: f287
user:
  changes:
    - '1'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User
  search:
    terms:
      - account
      - avatar
      - head
      - human
      - man
      - person
      - profile
  styles:
    - solid
    - regular
  unicode: f007
user-alt:
  changes:
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Alternate User
  search:
    terms:
      - account
      - avatar
      - head
      - human
      - man
      - person
      - profile
  styles:
    - solid
  unicode: f406
user-alt-slash:
  changes:
    - 5.0.11
  label: Alternate User Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4fa
user-astronaut:
  changes:
    - 5.0.11
  label: User Astronaut
  search:
    terms:
      - avatar
      - clothing
      - cosmonaut
      - space
      - suit
  styles:
    - solid
  unicode: f4fb
user-check:
  changes:
    - 5.0.11
  label: User Check
  search:
    terms: []
  styles:
    - solid
  unicode: f4fc
user-circle:
  changes:
    - '4.7'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User Circle
  search:
    terms:
      - account
      - avatar
      - head
      - human
      - man
      - person
      - profile
  styles:
    - solid
    - regular
  unicode: f2bd
user-clock:
  changes:
    - 5.0.11
  label: User Clock
  search:
    terms: []
  styles:
    - solid
  unicode: f4fd
user-cog:
  changes:
    - 5.0.11
  label: User Cog
  search:
    terms: []
  styles:
    - solid
  unicode: f4fe
user-edit:
  changes:
    - 5.0.11
  label: User Edit
  search:
    terms: []
  styles:
    - solid
  unicode: f4ff
user-friends:
  changes:
    - 5.0.11
  label: User Friends
  search:
    terms: []
  styles:
    - solid
  unicode: f500
user-graduate:
  changes:
    - 5.0.11
  label: User Graduate
  search:
    terms:
      - cap
      - clothing
      - commencement
      - gown
      - graduation
      - student
  styles:
    - solid
  unicode: f501
user-injured:
  changes:
    - 5.4.0
  label: User Injured
  search:
    terms:
      - cast
      - ouch
      - sling
  styles:
    - solid
  unicode: f728
user-lock:
  changes:
    - 5.0.11
  label: User Lock
  search:
    terms: []
  styles:
    - solid
  unicode: f502
user-md:
  changes:
    - '2'
    - 5.0.0
    - 5.0.3
    - 5.0.7
    - 5.0.11
  label: user-md
  search:
    terms:
      - doctor
      - job
      - medical
      - nurse
      - occupation
      - profile
  styles:
    - solid
  unicode: f0f0
user-minus:
  changes:
    - 5.0.11
  label: User Minus
  search:
    terms:
      - delete
      - negative
      - remove
  styles:
    - solid
  unicode: f503
user-ninja:
  changes:
    - 5.0.11
  label: User Ninja
  search:
    terms:
      - assassin
      - avatar
      - dangerous
      - deadly
      - sneaky
  styles:
    - solid
  unicode: f504
user-plus:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User Plus
  search:
    terms:
      - positive
      - sign up
      - signup
  styles:
    - solid
  unicode: f234
user-secret:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: User Secret
  search:
    terms:
      - clothing
      - coat
      - hat
      - incognito
      - privacy
      - spy
      - whisper
  styles:
    - solid
  unicode: f21b
user-shield:
  changes:
    - 5.0.11
  label: User Shield
  search:
    terms: []
  styles:
    - solid
  unicode: f505
user-slash:
  changes:
    - 5.0.11
  label: User Slash
  search:
    terms:
      - ban
      - remove
  styles:
    - solid
  unicode: f506
user-tag:
  changes:
    - 5.0.11
  label: User Tag
  search:
    terms: []
  styles:
    - solid
  unicode: f507
user-tie:
  changes:
    - 5.0.11
  label: User Tie
  search:
    terms:
      - avatar
      - business
      - clothing
      - formal
  styles:
    - solid
  unicode: f508
user-times:
  changes:
    - '4.3'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Remove User
  search:
    terms:
      - archive
      - delete
      - remove
      - x
  styles:
    - solid
  unicode: f235
users:
  changes:
    - '2'
    - 5.0.0
    - 5.0.3
    - 5.0.11
  label: Users
  search:
    terms:
      - people
      - persons
      - profiles
  styles:
    - solid
  unicode: f0c0
users-cog:
  changes:
    - 5.0.11
  label: Users Cog
  search:
    terms: []
  styles:
    - solid
  unicode: f509
ussunnah:
  changes:
    - 5.0.0
  label: us-Sunnah Foundation
  search:
    terms: []
  styles:
    - brands
  unicode: f407
utensil-spoon:
  changes:
    - 5.0.0
  label: Utensil Spoon
  search:
    terms:
      - spoon
  styles:
    - solid
  unicode: f2e5
utensils:
  changes:
    - 5.0.0
  label: Utensils
  search:
    terms:
      - cutlery
      - dinner
      - eat
      - food
      - knife
      - restaurant
      - spoon
  styles:
    - solid
  unicode: f2e7
vaadin:
  changes:
    - 5.0.0
  label: Vaadin
  search:
    terms: []
  styles:
    - brands
  unicode: f408
vector-square:
  changes:
    - 5.1.0
  label: Vector Square
  search:
    terms:
      - anchors
      - lines
      - object
  styles:
    - solid
  unicode: f5cb
venus:
  changes:
    - '4.3'
    - 5.0.0
  label: Venus
  search:
    terms:
      - female
  styles:
    - solid
  unicode: f221
venus-double:
  changes:
    - '4.3'
    - 5.0.0
  label: Venus Double
  search:
    terms: []
  styles:
    - solid
  unicode: f226
venus-mars:
  changes:
    - '4.3'
    - 5.0.0
  label: Venus Mars
  search:
    terms: []
  styles:
    - solid
  unicode: f228
viacoin:
  changes:
    - '4.3'
    - 5.0.0
  label: Viacoin
  search:
    terms: []
  styles:
    - brands
  unicode: f237
viadeo:
  changes:
    - '4.6'
    - 5.0.0
  label: Video
  search:
    terms: []
  styles:
    - brands
  unicode: f2a9
viadeo-square:
  changes:
    - '4.6'
    - 5.0.0
  label: Video Square
  search:
    terms: []
  styles:
    - brands
  unicode: f2aa
vial:
  changes:
    - 5.0.7
  label: Vial
  search:
    terms:
      - test tube
  styles:
    - solid
  unicode: f492
vials:
  changes:
    - 5.0.7
  label: Vials
  search:
    terms:
      - lab results
      - test tubes
  styles:
    - solid
  unicode: f493
viber:
  changes:
    - 5.0.0
    - 5.0.3
  label: Viber
  search:
    terms: []
  styles:
    - brands
  unicode: f409
video:
  changes:
    - '1'
    - 5.0.0
    - 5.0.9
  label: Video
  search:
    terms:
      - camera
      - film
      - movie
      - record
      - video-camera
  styles:
    - solid
  unicode: f03d
video-slash:
  changes:
    - 5.0.9
  label: Video Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4e2
vihara:
  changes:
    - 5.3.0
  label: Vihara
  search:
    terms:
      - buddhism
      - buddhist
      - building
      - monastery
  styles:
    - solid
  unicode: f6a7
vimeo:
  changes:
    - 5.0.0
  label: Vimeo
  search:
    terms: []
  styles:
    - brands
  unicode: f40a
vimeo-square:
  changes:
    - '4'
    - 5.0.0
  label: Vimeo Square
  search:
    terms: []
  styles:
    - brands
  unicode: f194
vimeo-v:
  changes:
    - '4.4'
    - 5.0.0
  label: Vimeo
  search:
    terms:
      - vimeo
  styles:
    - brands
  unicode: f27d
vine:
  changes:
    - '4.1'
    - 5.0.0
  label: Vine
  search:
    terms: []
  styles:
    - brands
  unicode: f1ca
vk:
  changes:
    - '3.2'
    - 5.0.0
  label: VK
  search:
    terms: []
  styles:
    - brands
  unicode: f189
vnv:
  changes:
    - 5.0.0
  label: VNV
  search:
    terms: []
  styles:
    - brands
  unicode: f40b
volleyball-ball:
  changes:
    - 5.0.5
  label: Volleyball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f45f
volume-down:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: Volume Down
  search:
    terms:
      - audio
      - lower
      - music
      - quieter
      - sound
      - speaker
  styles:
    - solid
  unicode: f027
volume-mute:
  changes:
    - 5.3.0
  label: Volume Mute
  search:
    terms: []
  styles:
    - solid
  unicode: f6a9
volume-off:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: Volume Off
  search:
    terms:
      - audio
      - music
      - mute
      - sound
  styles:
    - solid
  unicode: f026
volume-up:
  changes:
    - '1'
    - 5.0.0
    - 5.3.0
  label: Volume Up
  search:
    terms:
      - audio
      - higher
      - louder
      - music
      - sound
      - speaker
  styles:
    - solid
  unicode: f028
vote-yea:
  changes:
    - 5.5.0
  label: Vote Yea
  search:
    terms:
      - accept
      - cast
      - election
      - politics
      - positive
      - 'yes'
  styles:
    - solid
  unicode: f772
vr-cardboard:
  changes:
    - 5.4.0
  label: Cardboard VR
  search:
    terms:
      - google
      - reality
      - virtual
  styles:
    - solid
  unicode: f729
vuejs:
  changes:
    - 5.0.0
  label: Vue.js
  search:
    terms: []
  styles:
    - brands
  unicode: f41f
walking:
  changes:
    - 5.0.13
  label: Walking
  search:
    terms: []
  styles:
    - solid
  unicode: f554
wallet:
  changes:
    - 5.0.13
  label: Wallet
  search:
    terms: []
  styles:
    - solid
  unicode: f555
warehouse:
  changes:
    - 5.0.7
  label: Warehouse
  search:
    terms: []
  styles:
    - solid
  unicode: f494
water:
  changes:
    - 5.5.0
  label: Water
  search:
    terms: []
  styles:
    - solid
  unicode: f773
weebly:
  changes:
    - 5.1.0
  label: Weebly
  search:
    terms: []
  styles:
    - brands
  unicode: f5cc
weibo:
  changes:
    - '3.2'
    - 5.0.0
  label: Weibo
  search:
    terms: []
  styles:
    - brands
  unicode: f18a
weight:
  changes:
    - 5.0.7
  label: Weight
  search:
    terms:
      - measurement
      - scale
      - weight
  styles:
    - solid
  unicode: f496
weight-hanging:
  changes:
    - 5.1.0
  label: Hanging Weight
  search:
    terms:
      - anvil
      - heavy
      - measurement
  styles:
    - solid
  unicode: f5cd
weixin:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: Weixin (WeChat)
  search:
    terms: []
  styles:
    - brands
  unicode: f1d7
whatsapp:
  changes:
    - '4.3'
    - 5.0.0
  label: What's App
  search:
    terms: []
  styles:
    - brands
  unicode: f232
whatsapp-square:
  changes:
    - 5.0.0
  label: What's App Square
  search:
    terms: []
  styles:
    - brands
  unicode: f40c
wheelchair:
  changes:
    - '4'
    - 5.0.0
  label: Wheelchair
  search:
    terms:
      - handicap
      - person
  styles:
    - solid
  unicode: f193
whmcs:
  changes:
    - 5.0.0
  label: WHMCS
  search:
    terms: []
  styles:
    - brands
  unicode: f40d
wifi:
  changes:
    - '4.2'
    - 5.0.0
    - 5.3.0
  label: WiFi
  search:
    terms: []
  styles:
    - solid
  unicode: f1eb
wikipedia-w:
  changes:
    - '4.4'
    - 5.0.0
  label: Wikipedia W
  search:
    terms: []
  styles:
    - brands
  unicode: f266
wind:
  changes:
    - 5.4.0
    - 5.5.0
  label: Wind
  search:
    terms:
      - air
      - blow
      - breeze
      - fall
      - seasonal
  styles:
    - solid
  unicode: f72e
window-close:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Close
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f410
window-maximize:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Maximize
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d0
window-minimize:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Minimize
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d1
window-restore:
  changes:
    - '4.7'
    - 5.0.0
  label: Window Restore
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d2
windows:
  changes:
    - '3.2'
    - 5.0.0
  label: Windows
  search:
    terms:
      - microsoft
  styles:
    - brands
  unicode: f17a
wine-bottle:
  changes:
    - 5.4.0
  label: Wine Bottle
  search:
    terms:
      - alcohol
      - beverage
      - drink
      - glass
      - grapes
  styles:
    - solid
  unicode: f72f
wine-glass:
  changes:
    - 5.0.9
    - 5.1.0
  label: Wine Glass
  search:
    terms:
      - alcohol
      - beverage
      - drink
      - grapes
  styles:
    - solid
  unicode: f4e3
wine-glass-alt:
  changes:
    - 5.1.0
  label: Alternate Wine Glas
  search:
    terms:
      - alcohol
      - beverage
      - drink
      - grapes
  styles:
    - solid
  unicode: f5ce
wix:
  changes:
    - 5.1.0
  label: Wix
  search:
    terms: []
  styles:
    - brands
  unicode: f5cf
wizards-of-the-coast:
  changes:
    - 5.4.0
  label: Wizards of the Coast
  search:
    terms:
      - Dungeons & Dragons
      - d&d
      - dnd
      - fantasy
      - game
      - gaming
      - tabletop
  styles:
    - brands
  unicode: f730
wolf-pack-battalion:
  changes:
    - 5.0.12
  label: Wolf Pack Battalion
  search:
    terms: []
  styles:
    - brands
  unicode: f514
won-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Won Sign
  search:
    terms:
      - krw
  styles:
    - solid
  unicode: f159
wordpress:
  changes:
    - '4.1'
    - 5.0.0
  label: WordPress Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f19a
wordpress-simple:
  changes:
    - 5.0.0
  label: Wordpress Simple
  search:
    terms: []
  styles:
    - brands
  unicode: f411
wpbeginner:
  changes:
    - '4.6'
    - 5.0.0
  label: WPBeginner
  search:
    terms: []
  styles:
    - brands
  unicode: f297
wpexplorer:
  changes:
    - '4.7'
    - 5.0.0
  label: WPExplorer
  search:
    terms: []
  styles:
    - brands
  unicode: f2de
wpforms:
  changes:
    - '4.6'
    - 5.0.0
  label: WPForms
  search:
    terms: []
  styles:
    - brands
  unicode: f298
wpressr:
  changes:
    - 5.4.2
  label: wpressr
  search:
    terms:
      - rendact
  styles:
    - brands
  unicode: f3e4
wrench:
  changes:
    - '2'
    - 5.0.0
    - 5.0.13
  label: Wrench
  search:
    terms:
      - fix
      - settings
      - spanner
      - tool
      - update
  styles:
    - solid
  unicode: f0ad
x-ray:
  changes:
    - 5.0.7
  label: X-Ray
  search:
    terms:
      - radiological images
      - radiology
  styles:
    - solid
  unicode: f497
xbox:
  changes:
    - 5.0.0
  label: Xbox
  search:
    terms: []
  styles:
    - brands
  unicode: f412
xing:
  changes:
    - '3.2'
    - 5.0.0
  label: Xing
  search:
    terms: []
  styles:
    - brands
  unicode: f168
xing-square:
  changes:
    - '3.2'
    - 5.0.0
  label: Xing Square
  search:
    terms: []
  styles:
    - brands
  unicode: f169
y-combinator:
  changes:
    - '4.4'
    - 5.0.0
  label: Y Combinator
  search:
    terms: []
  styles:
    - brands
  unicode: f23b
yahoo:
  changes:
    - '4.1'
    - 5.0.0
    - 5.0.3
  label: Yahoo Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f19e
yandex:
  changes:
    - 5.0.0
  label: Yandex
  search:
    terms: []
  styles:
    - brands
  unicode: f413
yandex-international:
  changes:
    - 5.0.0
  label: Yandex International
  search:
    terms: []
  styles:
    - brands
  unicode: f414
yelp:
  changes:
    - '4.2'
    - 5.0.0
  label: Yelp
  search:
    terms: []
  styles:
    - brands
  unicode: f1e9
yen-sign:
  changes:
    - '3.2'
    - 5.0.0
  label: Yen Sign
  search:
    terms:
      - jpy
      - money
  styles:
    - solid
  unicode: f157
yin-yang:
  changes:
    - 5.3.0
  label: Yin Yang
  search:
    terms:
      - daoism
      - opposites
      - taoism
  styles:
    - solid
  unicode: f6ad
yoast:
  changes:
    - '4.6'
    - 5.0.0
    - 5.0.3
  label: Yoast
  search:
    terms: []
  styles:
    - brands
  unicode: f2b1
youtube:
  changes:
    - '3.2'
    - 5.0.0
  label: YouTube
  search:
    terms:
      - film
      - video
      - youtube-play
      - youtube-square
  styles:
    - brands
  unicode: f167
youtube-square:
  changes:
    - 5.0.3
  label: YouTube Square
  search:
    terms: []
  styles:
    - brands
  unicode: f431
zhihu:
  changes:
    - 5.2.0
  label: Zhihu
  search:
    terms: []
  styles:
    - brands
  unicode: f63f
