<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('auth.login');
});

Auth::routes();
Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::resource('clientes', App\Http\Controllers\ClienteController::class);
Route::resource('paquetes', App\Http\Controllers\PaqueteController::class);
Route::resource('apns', App\Http\Controllers\ApnController::class);
Route::resource('dedicadas', App\Http\Controllers\DedicadaController::class);
Route::resource('conmutadas', App\Http\Controllers\ConmutadaController::class);
Route::resource('correos', App\Http\Controllers\CorreoController::class);
Route::resource('navegacions', App\Http\Controllers\NavegacionController::class);
Route::resource('nubes', App\Http\Controllers\NubeController::class);
Route::resource('hostings', App\Http\Controllers\HostingController::class);
Route::resource('dominios', App\Http\Controllers\DominioController::class);
Route::resource('audioconferencias', App\Http\Controllers\AudioconferenciaController::class);
Route::resource('modulos', App\Http\Controllers\ModuloController::class);
Route::resource('sicemas', App\Http\Controllers\SicemaController::class);
Route::resource('users', App\Http\Controllers\UserController::class);
Route::resource('codigos', App\Http\Controllers\CodigoController::class);
Route::resource('solicitudes', App\Http\Controllers\SolicitudeController::class);
Route::resource('conciliaciones', App\Http\Controllers\ConciliacioneController::class);
Route::resource('contactos', App\Http\Controllers\ContactoController::class);
Route::resource('datadins', App\Http\Controllers\DatadinController::class);
Route::resource('ips', App\Http\Controllers\IpController::class);
Route::resource('ipminets', App\Http\Controllers\IpminetController::class);
Route::resource('provincias', App\Http\Controllers\ProvinciaController::class);
Route::resource('asignaciones', App\Http\Controllers\AsignacioneController::class);
Route::get('clientes/municipios/{id}', [App\Http\Controllers\ProvinciaController::class, 'getMunicipios']);
Route::put('Resetear/{id}', [App\Http\Controllers\UserController::class, 'reset'])->name('reset');
Route::put('Limpiar/{id}', [App\Http\Controllers\AsignacioneController::class, 'limpiar'])->name('limpiar');
Route::put('Mal/{id}', [App\Http\Controllers\AsignacioneController::class, 'mal'])->name('asignaciones.problemas');
Route::post('/facturacion', [App\Http\Controllers\ClienteController::class, 'facturacion'])->name('clientes.facturacion');
Route::get('/ver_factura/{cliente}', [App\Http\Controllers\ClienteController::class, 'ver_factura'])->name('clientes.ver_factura');
Route::get('/empresa', [App\Http\Controllers\AsignacioneController::class, 'getEmpresa'])->name('getEmpresa');
Route::get('/ver_monitor', [App\Http\Controllers\ClienteController::class, 'monitor'])->name('clientes.monitor');
