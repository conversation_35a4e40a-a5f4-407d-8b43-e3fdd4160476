<?php

namespace App\Http\Controllers;

use App\Models\Correo;
use App\Models\Empresa;
use App\Models\Paquete;
use Illuminate\Http\Request;

class CorreoController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $usuario = $request->get('usuario');
        $tipo = $request->get('tipo_correo');
    $trans = Correo::query()->orderby('usuario','ASC')
            ->usuario($usuario)
            ->tipo($tipo)
             ->whereHas('cliente', function ($query) use ($request) {
                $cliente = $request->get('ueb');
                $query->where('uebalimatic', 'LIKE', '%' . $cliente . '%');
            })
           // ->where($tipo, 'LIKE', '%' . 'personal' . '%');
            ->paginate(500);
    $empre= Empresa::all();

        return view('CLIENTES.CORREO.index',compact('trans','empre'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        if ($request->tipo_correo == 'corporativo'){
            $trans=new Correo();
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->usuario = $request->usuario;
        $trans->acceso = $request->acceso;
        $trans->tipo_correo = $request->tipo_correo;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'CORREO Insertado correctamente');
        }
        else {
        // Buscar cantidad de APN Autorizadas
                   $temp=paquete::orderby('id','DESC')
                   ->where('cliente_id',$request->cliente_id)->get();
                   $cant=0;
                   $cantCorreo=0;
                    foreach ($temp as $temp) {
                          $cant = $cant + $temp->correo;
                    }
        // *************************************
                   $temp2=correo::orderby('id','DESC')
                         ->where('cliente_id',$request->cliente_id)
                         ->where('tipo_correo','personal')->get();
                    $cantCorreo = count($temp2);
        if ($cant <= $cantCorreo){
           return back()->with('infod','Actualice o Contrate otro paquete de servicios de ALINET, el actual llego al tope');
        }
        $trans=new correo();
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->usuario = $request->usuario;
        $trans->acceso = $request->acceso;
        $trans->tipo_correo = $request->tipo_correo;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'CORREO Insertado correctamente');

        }

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\correo  $correo
     * @return \Illuminate\Http\Response
     */
    public function show(correo $correo)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\correo  $correo
     * @return \Illuminate\Http\Response
     */
    public function edit(correo $correo)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\correo  $correo
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $trans=correo::find($id);
        $trans->establecimiento = $request->establecimiento;
        $trans->usuario = $request->usuario;
        $trans->acceso = $request->acceso;
        $trans->tipo_correo = $request->tipo_correo;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'CORREO Actualizado correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\correo  $correo
     * @return \Illuminate\Http\Response
     */
    public function destroy($correo)
    {
        $trans = correo::find($correo);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'CORREO eliminado correctamente');
    }
}
