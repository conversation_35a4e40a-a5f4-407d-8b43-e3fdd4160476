<?php

namespace App\Http\Controllers;

use App\Models\Hosting;
use App\Models\Empresa;
use App\Models\Paquete;
use Illuminate\Http\Request;

class HostingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $nombre = $request->get('nombre');
        $bd = $request->get('bd');
        $plataforma = $request->get('plataforma');
    $trans = Hosting::query()->orderby('id','ASC')
            ->nombre($nombre)
            ->bd($bd)
            ->plataforma($plataforma)
            ->paginate(500);
    $empre= Empresa::all();

        return view('CLIENTES.HOSTING.index',compact('trans','empre'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
           // Buscar cantidad de HOSTING Autorizadas
                   $temp=Paquete::orderby('id','DESC')
                   ->where('cliente_id',$request->cliente_id)->get();
                   $cant=0;
                   $cantHosting=0;
                    foreach ($temp as $temp) {
                          $cant = $cant + $temp->hosting;
                    }
        // *************************************
                   $temp2=Hosting::orderby('id','DESC')
                         ->where('cliente_id',$request->cliente_id)->get();
                    $cantHosting = count($temp2);
                   if ($cant <= $cantHosting){
                   return back()->with('infod','Actualice o Contrate otro paquete de servicios de ALINET, el actual llego al tope');
                    }
        // Salvar HOSTING

        $trans=new hosting();
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->nombre_sitio = $request->nombre_sitio;
        $trans->base_de_datos = $request->base_de_datos;
        $trans->espacio = $request->espacio;
        $trans->plataforma = $request->plataforma;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'Hosting Insertado correctamente');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\hosting  $hosting
     * @return \Illuminate\Http\Response
     */
    public function show(hosting $hosting)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\hosting  $hosting
     * @return \Illuminate\Http\Response
     */
    public function edit(hosting $hosting)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\hosting  $hosting
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $trans=hosting::find($id);
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->nombre_sitio = $request->nombre_sitio;
        $trans->base_de_datos = $request->base_de_datos;
        $trans->espacio = $request->espacio;
        $trans->plataforma = $request->plataforma;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'Hosting Actualizado correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\hosting  $hosting
     * @return \Illuminate\Http\Response
     */
    public function destroy($hosting)
    {
        $trans = hosting::find($hosting);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'Hosting eliminado correctamente');
    }
}
