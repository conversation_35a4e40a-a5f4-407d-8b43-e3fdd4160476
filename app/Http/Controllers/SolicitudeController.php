<?php

namespace App\Http\Controllers;

use App\Models\Conciliacione;
use App\Models\Solicitude;
use App\Models\Empresa;
use Illuminate\Http\Request;

class SolicitudeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
      switch (auth()->user()->UEB) {

	case null:
        return back()->with('info','UD no esta Pendiente de Autorización');
       break;
    case "invitado":
    $trans = Solicitude::orderby('titulo','ASC')
            ->where('archivar',false)
            ->get();
    $empre= Empresa::all();

        return view('CLIENTES.SOLICITUDES.index',compact('trans','empre'));

        break;
    case (auth()->user()->UEB <> null):
         $trans = Solicitude::orderby('titulo','ASC')
            ->where('uebalimatic',auth()->user()->UEB)
            ->where('archivar',false)
            ->get();
    $empre= Empresa::all();

        return view('CLIENTES.SOLICITUDES.index',compact('trans','empre'));

        break;
     }

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $trans = new solicitude();
        $trans->cliente_id = $request->cliente_id;
        $trans->titulo = $request->titulo;
        $trans->dato = $request->dato ." Contacto Técnico:". $request->contacto ." Número de teléfono:". $request->telefono ;
        $trans->observacion = $request->observacion;
        $trans->uebalimatic = auth()->user()->UEB;
        $trans->save();
        $cliente=$trans->cliente_id;

        return redirect()->route('clientes.show',[$cliente])->with('info', 'SOLICITUD Insertada correctamente');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\solicitude  $solicitude
     * @return \Illuminate\Http\Response
     */
    public function show($request)
    {
       $trans= conciliacione::orderBy('fecha','DESC')->where('solicitud_id',$request)->get();
        return(view('CLIENTES.SOLICITUDES.conciliaciones',compact('trans')));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\solicitude  $solicitude
     * @return \Illuminate\Http\Response
     */
    public function edit(solicitude $solicitude)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\solicitude  $solicitude
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $trans = solicitude::find($id);
        $trans->titulo = $request->titulo;
        $trans->dato = $request->dato;
        $trans->observacion = $request->observacion;
        $trans->archivar = $request->archivar;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'SOLICITUD Actualizada correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\solicitude  $solicitude
     * @return \Illuminate\Http\Response
     */
    public function destroy($solicitude)
    {
        $trans = solicitude::find($solicitude);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'SOLICITUD Eliminada correctamente');
    }

}
