<?php

namespace App\Http\Controllers;

use App\Models\Nube;
use App\Models\Empresa;
use App\Models\Paquete;
use Illuminate\Http\Request;

class NubeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $usuario = $request->get('usuario');
    $trans = Nube::query()->orderby('id','ASC')
            ->usuario($usuario)
            ->paginate(500);
    $empre= Empresa::all();

        return view('CLIENTES.NUBE.index',compact('trans','empre'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
          // Buscar cantidad de APN Autorizadas
                   $temp=Paquete::orderby('id','DESC')
                   ->where('cliente_id',$request->cliente_id)->get();
                   $cant=0;
                   $cantNube=0;
                    foreach ($temp as $temp) {
                          $cant = $cant + $temp->nube;
                    }
        // *************************************
                   $temp2=Nube::orderby('id','DESC')
                         ->where('cliente_id',$request->cliente_id)->get();
                    $cantNube = count($temp2);
                   if ($cant <= $cantNube){
                   return back()->with('infod','Actualice o Contrate otro paquete de servicios de ALINET, el actual llego al tope');
                    }
        // Salvar NUBE
        $trans=new nube();
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->usuario = $request->usuario;
        $trans->espacio = $request->espacio.$request->medida ;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'NUBE Insertada correctamente');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\nube  $nube
     * @return \Illuminate\Http\Response
     */
    public function show(nube $nube)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\nube  $nube
     * @return \Illuminate\Http\Response
     */
    public function edit(nube $nube)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\nube  $nube
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $trans=nube::find($id);
        $trans->establecimiento = $request->establecimiento;
        $trans->usuario = $request->usuario;
        $trans->espacio = $request->espacio;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'NUBE Actualizada correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\nube  $nube
     * @return \Illuminate\Http\Response
     */
    public function destroy($nube)
    {
        $trans = nube::find($nube);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'NUBE eliminada correctamente');
    }
}
