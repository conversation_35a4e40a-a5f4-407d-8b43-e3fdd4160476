<?php

namespace App\Http\Controllers;

use App\Models\Navegacion;
use App\Models\Empresa;
use Illuminate\Http\Request;

class NavegacionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $acceso = $request->get('acceso');
        $navegacion = $request->get('navegacion');
    $trans = Navegacion::query()->orderby('id','ASC')
            ->acceso($acceso)
            ->navegacion($navegacion)
            ->paginate(500);
         $empre= Empresa::all();

        return view('CLIENTES.NAVEGACION.index',compact('trans','empre'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
    $trans=new Navegacion();
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->acceso = $request->acceso;
        $trans->ip_navegacion = $request->ip_navegacion;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'NAVEGACION Insertada correctamente');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\navegacion  $navegacion
     * @return \Illuminate\Http\Response
     */
    public function show(navegacion $navegacion)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\navegacion  $navegacion
     * @return \Illuminate\Http\Response
     */
    public function edit(navegacion $navegacion)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\navegacion  $navegacion
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $trans=navegacion::find($id);
        $trans->establecimiento = $request->establecimiento;
        $trans->acceso = $request->acceso;
        $trans->ip_navegacion = $request->ip_navegacion;
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'NAVEGACION Actualizada correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\navegacion  $navegacion
     * @return \Illuminate\Http\Response
     */
    public function destroy($navegacion)
    {
        $trans = navegacion::find($navegacion);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'NAVEGACION eliminada correctamente');
    }
}
