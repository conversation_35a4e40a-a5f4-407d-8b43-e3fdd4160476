<?php

namespace App\Http\Controllers;
use App\Models\Empresa;
use App\Models\Cliente;
use App\Models\Apn;
use App\Models\Dedicada;
use App\Models\Conmutada;
use App\Models\Correo;
use App\Models\Navegacion;
use App\Models\Nube;
use App\Models\Hosting;
use App\Models\Dominio;
use App\Models\Audioconferencia;
use App\Models\Codigo;
use App\Models\Modulo;
use App\Models\Datadin;
use App\Models\Sicema;
use App\Models\Solicitude;
use App\Models\Contacto;
use App\Models\Provincia;
use App\Models\Municipio;
use App\Models\Paquete;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\clienteRequest;
use Illuminate\Pagination\Paginator;

class ClienteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index(Request $request)
    {
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $entidad = $request->get('entidad');
        $contrato = $request->get('no_contrato');
        $trans=cliente::orderby('id','DESC')
            //filtrado de consultas a campos relacionados (empresa es el metodo de la relacion llama al modelo empresa y nombre es el nombre del campo de la tabla relacionada , )
            ->whereHas('empresa', function ($query) use ($request) {
                $empresa = $request->get('empresa');
                $query->where('nombre', 'LIKE', '%' . $empresa . '%');
            })
            //filtrado scope por entidades y contrato
            ->entidad($entidad)
            ->contrato($contrato)
            ->paginate(1000);
        // enviamos la lista de empresa y id de la tabla empresas
        $empre = empresa::pluck('nombre','id');
        $codigos = codigo::pluck('codigo','id');
        return view('CLIENTES.cliente',compact('trans','empre','codigos'));

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(clienteRequest $request)
    {
        $trans =new cliente;
        $trans->empresa_id = $request->empresa_id;
        $trans->entidad = $request->entidad;
        $trans->direccion = $request->direccion;
        $trans->provincia = $request->provincia;
        $trans->municipio = $request->municipio;
        $trans->uebalimatic = $request->uebalimatic;
        $trans->no_contrato = $request->no_contrato;
        $trans->save();
        return redirect()->route('clientes.index')->with('info', 'EMPRESA Insertada correctamente');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\cliente  $cliente
     * @return \Illuminate\Http\Response
     */
    public function show($cliente)
    {
       $buscar=cliente::find($cliente);
       $trans1=empresa::find($buscar->empresa_id);
       $empresa=$trans1->nombre;
       $codigoss = codigo::all();
       $provincias = provincia::pluck('nombre','id');
       $municipios = municipio::pluck('nombre','id');
      // $codigos = codigo::where('UEB',auth()->user()->UEB)->pluck('descripcion','id');
     //  $codigos = codigo::pluck('descripcion','id');
       $codigosEnlaces = codigo::where('servicio','tic')->pluck('descripcion','id');
       $codigosDatadins = codigo::where('servicio','datadin')->pluck('descripcion','id');
       $codigosPaquete = codigo::where('servicio','paquete')->pluck('descripcion','id');
       $codigosSicema = codigo::where('servicio','sicema')->pluck('descripcion','id');
       $paquetes = paquete::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $apns = apn::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $dedicadas = dedicada::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
//       $conmutadas = conmutada::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $correos = correo::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $navegacions = navegacion::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $nubes = nube::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $hostings = hosting::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $dominios = dominio::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $modulos = modulo::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $sicemas = sicema::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $datadins = datadin::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       $solicitudes = solicitude::orderBy('id','DESC')->where('cliente_id',$cliente)->where('archivar',false)->get();
       $contactos = contacto::orderBy('id','DESC')->where('cliente_id',$cliente)->get();
       return(view('CLIENTES.servicio',compact('cliente','empresa','apns','dedicadas','correos','navegacions','nubes','hostings','dominios','codigosEnlaces','codigoss','modulos','sicemas','solicitudes','contactos','provincias','municipios','paquetes','datadins','codigosDatadins','codigosPaquete','codigosSicema')));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\cliente  $cliente
     * @return \Illuminate\Http\Response
     */
    public function edit(cliente $cliente)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\cliente  $cliente
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $trans=cliente::find($id);
        $trans->empresa_id = $request->empresa_id;
        $trans->entidad = $request->entidad;
        $trans->direccion = $request->direccion;
        $trans->provincia = $request->provincia;
        $trans->municipio = $request->municipio;
        $trans->uebalimatic = $request->uebalimatic;
        $trans->no_contrato = $request->no_contrato;
        $trans->save();
        return redirect()->route('clientes.index')->with('info', 'EMPRESA Actualizada correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\cliente  $cliente
     * @return \Illuminate\Http\Response
     */
    public function destroy($cliente)
    {
        $trans = cliente::find($cliente);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'CLIENTE eliminado correctamente');
    }




    public function facturacion(Request $request)
    {
        $clientes = cliente::where('uebalimatic', '=' ,$request->ueb)->get();
        $tdedicada = 0;
        $tsicema = 0;
        $tpaquete = 0;
        $tdatadin = 0;
        $total_facturas = 0;
        $mydedicada = array();
        $mypaquete = array();
        $mysicema = array();
        $mydatadin = array();
        $sudedicada = 0;
        $supaquete = 0;
        $susicema = 0;
        $sudatadin = 0;

               //*************************************DEDICADA******************************************************
           foreach ($clientes as $cliente) {

               $dedicadas = dedicada::where('cliente_id', '=', $cliente->id)->get();
               foreach ($dedicadas as $dedicada) {
                   $tdedicada = $tdedicada + $dedicada->codigo->precio;
                   $sudedicada = $sudedicada + $dedicada->codigo->precio;
               }
               array_push($mydedicada, $sudedicada);
               $sudedicada = 0;

               //**************************************PAQUETE*****************************************************
             }
               foreach ($clientes as $cliente) {

               $paquetes = paquete::where('cliente_id', '=', $cliente->id)->get();
               foreach ($paquetes as $paquete) {
                   $tpaquete = $tpaquete + $paquete->codigo->precio;
                   $supaquete = $supaquete + $paquete->codigo->precio;
               }
               array_push($mypaquete, $supaquete);
               $supaquete = 0;
               }
               //**************************************Sicema*****************************************************
               foreach ($clientes as $cliente) {

               $sicemas = sicema::where('cliente_id', '=', $cliente->id)->get();
               foreach ($sicemas as $sicema) {

                  $tsicema = $tsicema + $sicema->codigo->precio;
                   $susicema = $susicema + $sicema->codigo->precio;
               }
               array_push($mysicema, $susicema);
               $susicema = 0;

               }
              //**************************************PAQUETE*****************************************************
              foreach ($clientes as $cliente) {

               $datadins = datadin::where('cliente_id', '=', $cliente->id)->get();
               foreach ($datadins as $datadin) {

                  $tdatadin = $tdatadin + $datadin->codigo->precio;
                   $sudatadin = $sudatadin + $datadin->codigo->precio;
               }
               array_push($mydatadin, $sudatadin);
               $sudatadin = 0;
               //**************************************PAQUETE*****************************************************
             }




        $total_facturas = $tdedicada + $tpaquete + $tsicema + $tdatadin;
       $clientes = cliente::where('uebalimatic', '=' ,$request->ueb)->get();
        //*******************************
       // $libro = (object)$mydedicada;
     //  dd($mydedicada);

          return(view('CLIENTES.FACTURACION.facturacion',compact('clientes','total_facturas','mydedicada','mypaquete','mysicema','mydatadin')));
    }


    public function ver_factura($cliente)
    {
        $clientess = cliente::find($cliente) ;
        $nombre_empresa = $clientess->empresa->nombre;

        $dedicadas = dedicada::query()->where('cliente_id', '=' ,$cliente)
            ->with('codigo:id,descripcion,precio')
        ->orderby('codigo_id','DESC')
        ->get();


        $paquetes = paquete::where('cliente_id', '=' ,$cliente)
        ->orderby('codigo_id','DESC')
        ->get();

        $sicemas = sicema::where('cliente_id', '=' ,$cliente)
            ->orderby('codigo_id','DESC')
            ->get();

        $datadins = datadin::where('cliente_id', '=' ,$cliente)
            ->orderby('codigo_id','DESC')
            ->get();

dd($dedicadas);
        $codigos = codigo::all();

        return(view('CLIENTES.FACTURACION.ver_factura',compact('codigos','dedicadas','sicemas','nombre_empresa','paquetes','datadins')));
    }
     public function monitor(Request $request)
    {
       // dd('vista lista');
       $dedicadas = dedicada::orderBy('id','DESC')
       ->whereHas('cliente', function ($query) use ($request) {
                $cliente = auth()->user()->UEB;
                $query->where('uebalimatic', 'LIKE', '%' . $cliente . '%');
            })
       ->get();
      $empresas = empresa::all();

        return(view('CLIENTES.MONITOR.monitor',compact('dedicadas','empresas')));
    }

}
