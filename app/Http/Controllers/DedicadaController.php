<?php

namespace App\Http\Controllers;
use App\Models\Dedicada;
use App\Http\Requests\DedicadaRequest;
use App\Models\Provincia;
use App\Models\Empresa;
use Illuminate\Http\Request;

class DedicadaController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (auth()->user()->UEB==null) return back()->with('info','UD no esta Pendiente de Autorización');
        $ina = $request->get('ina');
        $lan = $request->get('lan');
        $lc= $request->get('lc');
        $velocidad = $request->get('velocidad');

    $trans = Dedicada::query()->orderby('velocidad','DESC')
            ->ina($ina)
            ->lan($lan)
            ->lc($lc)
            ->velocidad($velocidad)
            ->whereHas('cliente', function ($query) use ($request) {
                $cliente = $request->get('ueb');
                $query->where('uebalimatic', 'LIKE', '%' . $cliente . '%');
            })
            ->paginate(500);
            $nacional=0;
            $internacional=0;
            $alimatic=0;
        foreach ($trans as $tran) {
            if ($tran->acceso=='nacional'){
             $nacional++;
              }elseif ($tran->acceso=='internacional'){
                   $internacional++;
              }else{
              $alimatic++;
          }
         }

         $empre= Empresa::all();
        return view('CLIENTES.DEDICADAS.index',compact('trans','empre','alimatic','nacional','internacional'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(dedicadaRequest $request)
    {
        $trans=new dedicada;
        $trans->cliente_id = $request->cliente_id;
        $trans->establecimiento = $request->establecimiento;
        $trans->ina = $request->ina;
        $trans->identificador = $request->identificador;
        $trans->lan = $request->lan;
        $trans->wan = $request->wan;
        $trans->ip_monitor = $request->ip_monitor;
        $trans->velocidad= $request->velocidad;
        $trans->acceso= $request->acceso;
        $trans->codigo_id = $request->codigo_id;
        $trans->direccion = $request->direccion;
        $trans->cant_usuarios = $request->cant_usuarios;
        $trans->cant_pc = $request->cant_pc;
        if($request->provincia_id<>0){
            $trans->provincia_id = $request->provincia_id;
        }
         if($request->municipio_id<>'placeholder'){
            $trans->municipio_id = $request->municipio_id;
        }
        $trans->save();

        $cliente=$trans->cliente_id;

        return redirect()->route('clientes.show',[$cliente])->with('info', 'DEDICADA Insertada correctamente');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\dedicada  $dedicada
     * @return \Illuminate\Http\Response
     */
    public function show(dedicada $dedicada)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\dedicada  $dedicada
     * @return \Illuminate\Http\Response
     */
    public function edit(dedicada $dedicada)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\dedicada  $dedicada
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,  $id)
    {
        $trans=dedicada::find($id);
        $trans->establecimiento = $request->establecimiento;
        $trans->ina = $request->ina;
        $trans->identificador = $request->identificador;
        $trans->lan = $request->lan;
        $trans->wan = $request->wan;
        $trans->ip_monitor = $request->ip_monitor;
        $trans->velocidad= $request->velocidad;
        $trans->acceso= $request->acceso;
        $trans->codigo_id = $request->codigo_id;
        $trans->direccion = $request->direccion;
        $trans->cant_usuarios = $request->cant_usuarios;
        $trans->cant_pc = $request->cant_pc;
        if($request->provincia_id<>0){
            $trans->provincia_id = $request->provincia_id;
        }
         if($request->municipio_id<>'placeholder'){
            $trans->municipio_id = $request->municipio_id;
        }
        $trans->save();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'DEDICADA Actualizada correctamente');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\dedicada  $dedicada
     * @return \Illuminate\Http\Response
     */
    public function destroy($dedicada)
    {
        $trans = dedicada::find($dedicada);
        $trans->delete();
        $cliente=$trans->cliente_id;
        return redirect()->route('clientes.show',[$cliente])->with('info', 'DEDICADA eliminada correctamente');
    }
}
