<?php

namespace App\Models;

use App\Http\GlobalScope\UEBScopeModel;
use Illuminate\Database\Eloquent\Model;

class Cliente extends UEBScopeModel
{
    public function empresa(){
        return $this->belongsTo(Empresa::class);
    }
    public function scopeEntidad($query, $entidad)
    {
        if ($entidad != ""){
            return $query->where('entidad','LIKE', "%$entidad%");
        }
    }

    public function scopeContrato($query, $contrato)
    {
        if ($contrato != ""){
            return $query->where('no_contrato','LIKE', "%$contrato%");
        }
    }
}
