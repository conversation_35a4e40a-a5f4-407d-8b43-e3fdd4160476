<?php

namespace App\Models;

use App\Http\GlobalScope\UEBScopeModel;
use Illuminate\Database\Eloquent\Model;

class Nube extends UEBScopeModel
{
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function codigo(){
        return $this->belongsTo(Codigo::class);
    }
    public function scopeUsuario($query, $usuario)
    {
        if ($usuario != ""){
            return $query->where('usuario','LIKE', "%$usuario%");
        }
    }
}
