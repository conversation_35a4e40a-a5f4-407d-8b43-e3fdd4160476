<?php

namespace App\Models;

use App\Http\GlobalScope\UEBScopeModel;
use Illuminate\Database\Eloquent\Model;

class Dominio extends UEBScopeModel
{
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeNombre($query, $nombre)
    {
        if ($nombre != ""){
            return $query->where('nombre_dominio','LIKE', "%$nombre%");
        }
    }
    public function scopeTipo($query, $tipo)
    {
        if ($tipo != ""){
            return $query->where('tipo','LIKE', "%$tipo%");
        }
    }
}
