<?php

namespace App\Models;

use App\Models\Scopes\UebScope;
use Illuminate\Database\Eloquent\Model;

class Apn extends Model
{
    protected static function boot()
    {
        parent::boot();
        self::addGlobalScope(new UebScope());
    }
    
    public function codigo(){
        return $this->belongsTo(Codigo::class);
    }
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeMovil($query, $movil)
    {
        if ($movil != ""){
            return $query->where('no_movil','LIKE', "%$movil%");
        }
    }

    public function scopeNombre($query, $nombre)
    {
        if ($nombre != ""){
            return $query->where('nombre','LIKE', "%$nombre%");
        }
    }
}
