<?php

namespace App\Models;

use App\Models\Scopes\UebScope;
use Illuminate\Database\Eloquent\Model;

class Correo extends Model
{
    protected static function boot()
    {
        parent::boot();
        self::addGlobalScope(new UebScope());
    }

    public function codigo(){
        return $this->belongsTo(Codigo::class, 'codigo_id');
    }
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeUsuario($query, $usuario)
    {
        if ($usuario != ""){
            return $query->where('usuario','LIKE', "%$usuario%");
        }
    }

    public function scopeTipo($query, $tipo)
    {
        if ($tipo != ""){
            return $query->where('tipo_correo','LIKE', "%$tipo%");
        }
    }
}
