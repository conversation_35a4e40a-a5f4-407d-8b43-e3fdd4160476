<?php

namespace App\Models;

use App\Http\GlobalScope\UEBScopeModel;
use Illuminate\Database\Eloquent\Model;

class Hosting extends UEBScopeModel
{
    public function codigo(){
        return $this->belongsTo(Codigo::class);
    }
    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeNombre($query, $nombre)
    {
        if ($nombre != ""){
            return $query->where('nombre_sitio','LIKE', "%$nombre%");
        }
    }

    public function scopeBd($query, $bd)
    {
        if ($bd != ""){
            return $query->where('base_de_datos','LIKE', "%$bd%");
        }
    }

    public function scopePlataforma($query, $plataforma)
    {
        if ($plataforma != ""){
            return $query->where('plataforma','LIKE', "%$plataforma%");
        }
    }
}
