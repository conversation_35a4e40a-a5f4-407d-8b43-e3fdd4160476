<?php

namespace App\Models;

use App\Models\Scopes\UebScope;
use Illuminate\Database\Eloquent\Model;

class Navegacion extends Model
{
    protected static function boot()
    {
        parent::boot();
        self::addGlobalScope(new UebScope());
    }

    public function cliente(){
        return $this->belongsTo(Cliente::class);
    }
    public function scopeNavegacion($query, $ip_navegacion)
    {
        if ($ip_navegacion != ""){
            return $query->where('ip_navegacion','LIKE', "%$ip_navegacion%");
        }
    }
    public function scopeAcceso($query, $acceso)
    {
        if ($acceso != ""){
            return $query->where('acceso','LIKE', "%$acceso%");
        }
    }
}
