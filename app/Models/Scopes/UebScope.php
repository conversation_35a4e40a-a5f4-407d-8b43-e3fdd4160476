<?php

namespace App\Models\Scopes;

use App\Models\Cliente;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class UebScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if(!in_array(auth()->user()->UEB, config('constants.ueb'))){
            return;
        }

        if(!$model instanceof Cliente) {
            $builder->whereHas('cliente', function (Builder $builder) {
                $builder->where('uebalimatic', 'LIKE', '%' . auth()->user()->UEB . '%');
            });
        }
    }
}
